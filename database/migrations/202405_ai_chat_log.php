<?php
use think\migration\Migration;

class AiChatLog extends Migration
{
    public function change()
    {
        $table = $this->table('ai_chat_log');
        $table->addColumn('user_id', 'integer', ['default' => 0, 'comment' => '用户ID'])
            ->addColumn('session_id', 'string', ['limit' => 64, 'default' => '', 'comment' => '会话ID'])
            ->addColumn('role', 'string', ['limit' => 16, 'default' => '', 'comment' => '角色:user/assistant'])
            ->addColumn('content', 'text', ['comment' => '内容'])
            ->addColumn('create_time', 'integer', ['default' => 0, 'comment' => '创建时间'])
            ->addColumn('update_time', 'integer', ['default' => 0, 'comment' => '更新时间'])
            ->addColumn('delete_time', 'integer', ['default' => 0, 'comment' => '软删除'])
            ->create();
    }
} 