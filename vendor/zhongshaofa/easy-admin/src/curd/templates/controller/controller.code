<?php

namespace {{controllerNamespace}};

use app\common\controller\AdminController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;
use think\App;

/**
 * @ControllerAnnotation(title="{{controllerAnnotation}}")
 */
class {{controllerName}} extends AdminController
{

    use \app\admin\traits\Curd;

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->model = new {{modelFilename}}();
        {{selectList}}
    }

    {{indexMethod}}
}