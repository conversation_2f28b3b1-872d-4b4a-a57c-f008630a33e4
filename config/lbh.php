<?php
// +----------------------------------------------------------------------
// | 自定义常量配置
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    //生长期设定
    'stage' => [
        ['value' => 1, 'text' => '育雏期'],
        ['value' => 2, 'text' => '育成期'],
        ['value' => 3, 'text' => '产蛋期'],
        ['value' => 4, 'text' => '产蛋高峰期']
    ],
    //鸡蛋计量单位
    'egg_units'=>['斤','公斤','箱','件','枚'],
    //鸡蛋类型
    'egg_class'=>[1=>'合格蛋',2=>'双黄蛋',3=>'次品蛋',4=>'破蛋'],
    //报名类别
    'warn_type'=>['','离线报警','高温报警','送变器故障报警'],
    //鸡蛋品种
    'eggType'=>['1'=>'红蛋','2'=>'粉蛋','3'=>'白蛋'],
    // 客户属性
    'user_attr'=>['0'=>'全部客户','1'=>'设备客户','2'=>'生产客户','3'=>'临时客户'],
    //重量单位
    'weight_unit'=>[1=>'斤',2=>'公斤',3=>'吨',4=>'克',5=>'千克'],
    //重量换算-斤为基数
    'weight_convert'=>[1=>1,2=>2,3=>2000,4=>0.002,5=>2],

];