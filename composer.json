{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "^1.0", "topthink/think-view": "^1.0", "doctrine/annotations": "^1.8", "topthink/think-captcha": "^3.0", "aliyuncs/oss-sdk-php": "^2.3", "qcloud/cos-sdk-v5": "^2.0", "qiniu/php-sdk": "^7.2", "alibabacloud/client": "^1.5", "jianyan74/php-excel": "^1.0", "topthink/think-worker": "^3.0", "workerman/workerman-for-win": "^3.5", "alibabacloud/sdk": "^1.8", "alibabacloud/dysmsapi-20170525": "1.0.1", "alibabacloud/darabonba-openapi": "^0.1.8", "singka/singka-sms": "^1.6"}, "require-dev": {"symfony/var-dumper": "^4.2", "eaglewu/swoole-ide-helper": "dev-master"}, "autoload": {"psr-4": {"app\\": "app", "addons\\": "addons", "EasyAdmin\\": "vendor/zhongshaofa/easy-admin/src", "ServiceSwoole\\": "vendor/zhongshaofa/service-swoole/src"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "secure-http": false}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.aliyun.com/composer/"}}}