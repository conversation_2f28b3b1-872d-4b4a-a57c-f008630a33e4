{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "0a715593bf6e8b130129379d33429ef6", "packages": [{"name": "adbario/php-dot-notation", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/eee4fc81296531e6aafba4c2bbccfc5adab1676e", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "time": "2019-01-01T23:59:15+00:00"}, {"name": "alibabacloud/client", "version": "1.5.22", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "900c5b11aefe542048070fb2287e014dd2833e51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/900c5b11aefe542048070fb2287e014dd2833e51", "reference": "900c5b11aefe542048070fb2287e014dd2833e51", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "clagiordano/weblibs-configmanager": "^1.0", "danielstjules/stringy": "^3.1", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3", "mtdowling/jmespath.php": "^2.4", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Client\\": "src"}, "files": ["src/Functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "time": "2020-05-12T02:06:59+00:00"}, {"name": "alibabacloud/credentials", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/aliyun/credentials-php.git", "reference": "8c30db1ddf07318b9ee79aa7af46e0bd6be697da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/credentials-php/zipball/8c30db1ddf07318b9ee79aa7af46e0bd6be697da", "reference": "8c30db1ddf07318b9ee79aa7af46e0bd6be697da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Credentials for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "credentials", "library", "sdk", "tool"], "time": "2020-12-24T10:20:12+00:00"}, {"name": "alibabacloud/darabonba-openapi", "version": "0.1.8", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba-openapi.git", "reference": "c577dea4415b6812d52d9e970a517932eed4a997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba-openapi/zipball/c577dea4415b6812d52d9e970a517932eed4a997", "reference": "c577dea4415b6812d52d9e970a517932eed4a997", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/credentials": "^1.1", "alibabacloud/openapi-util": "^0.1.7", "alibabacloud/tea-utils": "^0.2.0", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client", "time": "2021-02-20T10:03:26+00:00"}, {"name": "alibabacloud/dysmsapi-20170525", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/Dysmsapi-20170525.git", "reference": "99a0098c844af962147a765abd4dc967ff311a1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/Dysmsapi-20170525/zipball/99a0098c844af962147a765abd4dc967ff311a1c", "reference": "99a0098c844af962147a765abd4dc967ff311a1c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/darabonba-openapi": "^0.1.0", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/tea-utils": "^0.2.0", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Dysmsapi\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Dysmsapi (20170525) SDK Library for PHP", "support": {"source": "https://github.com/alibabacloud-sdk-php/Dysmsapi-20170525/tree/1.0.1"}, "time": "2021-01-04T15:20:30+00:00"}, {"name": "alibabacloud/endpoint-util", "version": "0.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/endpoint-util.git", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/endpoint-util/zipball/f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Endpoint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Endpoint Library for PHP", "time": "2020-06-04T10:57:15+00:00"}, {"name": "alibabacloud/openapi-util", "version": "0.1.7", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-util.git", "reference": "a8b795b61049ad3aac27434b19d637e128d596c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-util/zipball/a8b795b61049ad3aac27434b19d637e128d596c4", "reference": "a8b795b61049ad3aac27434b19d637e128d596c4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2", "lizhichao/one-sm": "^1.5", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\OpenApiUtil\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi <PERSON>", "time": "2021-02-10T02:26:21+00:00"}, {"name": "alibabacloud/sdk", "version": "1.8.945", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php.git", "reference": "4b271022764501a77f82a86454bd6f247abf5e5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php/zipball/4b271022764501a77f82a86454bd6f247abf5e5a", "reference": "4b271022764501a77f82a86454bd6f247abf5e5a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/client": "^1.5", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "php": ">=5.5"}, "replace": {"alibabacloud/aas": "self.version", "alibabacloud/actiontrail": "self.version", "alibabacloud/adb": "self.version", "alibabacloud/aegis": "self.version", "alibabacloud/afs": "self.version", "alibabacloud/airec": "self.version", "alibabacloud/alidns": "self.version", "alibabacloud/alikafka": "self.version", "alibabacloud/alimt": "self.version", "alibabacloud/aliprobe": "self.version", "alibabacloud/aliyuncvc": "self.version", "alibabacloud/appmallsservice": "self.version", "alibabacloud/arms": "self.version", "alibabacloud/arms4finance": "self.version", "alibabacloud/baas": "self.version", "alibabacloud/batchcompute": "self.version", "alibabacloud/bss": "self.version", "alibabacloud/bssopenapi": "self.version", "alibabacloud/cas": "self.version", "alibabacloud/cbn": "self.version", "alibabacloud/ccc": "self.version", "alibabacloud/ccs": "self.version", "alibabacloud/cdn": "self.version", "alibabacloud/cds": "self.version", "alibabacloud/cf": "self.version", "alibabacloud/chatbot": "self.version", "alibabacloud/cloudapi": "self.version", "alibabacloud/cloudauth": "self.version", "alibabacloud/cloudesl": "self.version", "alibabacloud/cloudmarketing": "self.version", "alibabacloud/cloudphoto": "self.version", "alibabacloud/cloudwf": "self.version", "alibabacloud/cms": "self.version", "alibabacloud/commondriver": "self.version", "alibabacloud/companyreg": "self.version", "alibabacloud/cr": "self.version", "alibabacloud/crm": "self.version", "alibabacloud/cs": "self.version", "alibabacloud/csb": "self.version", "alibabacloud/cusanalyticsconline": "self.version", "alibabacloud/dataworkspublic": "self.version", "alibabacloud/dbs": "self.version", "alibabacloud/dcdn": "self.version", "alibabacloud/dds": "self.version", "alibabacloud/democenter": "self.version", "alibabacloud/dm": "self.version", "alibabacloud/dmsenterprise": "self.version", "alibabacloud/domain": "self.version", "alibabacloud/domainintl": "self.version", "alibabacloud/drcloud": "self.version", "alibabacloud/drds": "self.version", "alibabacloud/dts": "self.version", "alibabacloud/dybaseapi": "self.version", "alibabacloud/dyplsapi": "self.version", "alibabacloud/dypnsapi": "self.version", "alibabacloud/dysmsapi": "self.version", "alibabacloud/dyvmsapi": "self.version", "alibabacloud/eci": "self.version", "alibabacloud/ecs": "self.version", "alibabacloud/ecsinc": "self.version", "alibabacloud/edas": "self.version", "alibabacloud/ehpc": "self.version", "alibabacloud/elasticsearch": "self.version", "alibabacloud/emr": "self.version", "alibabacloud/ess": "self.version", "alibabacloud/facebody": "self.version", "alibabacloud/fnf": "self.version", "alibabacloud/foas": "self.version", "alibabacloud/ft": "self.version", "alibabacloud/goodstech": "self.version", "alibabacloud/gpdb": "self.version", "alibabacloud/green": "self.version", "alibabacloud/hbase": "self.version", "alibabacloud/hiknoengine": "self.version", "alibabacloud/hpc": "self.version", "alibabacloud/hsm": "self.version", "alibabacloud/httpdns": "self.version", "alibabacloud/idst": "self.version", "alibabacloud/imageaudit": "self.version", "alibabacloud/imageenhan": "self.version", "alibabacloud/imagerecog": "self.version", "alibabacloud/imagesearch": "self.version", "alibabacloud/imageseg": "self.version", "alibabacloud/imm": "self.version", "alibabacloud/industrybrain": "self.version", "alibabacloud/iot": "self.version", "alibabacloud/iqa": "self.version", "alibabacloud/itaas": "self.version", "alibabacloud/ivision": "self.version", "alibabacloud/ivpd": "self.version", "alibabacloud/jaq": "self.version", "alibabacloud/jarvis": "self.version", "alibabacloud/jarvispublic": "self.version", "alibabacloud/kms": "self.version", "alibabacloud/linkedmall": "self.version", "alibabacloud/linkface": "self.version", "alibabacloud/linkwan": "self.version", "alibabacloud/live": "self.version", "alibabacloud/lubancloud": "self.version", "alibabacloud/lubanruler": "self.version", "alibabacloud/market": "self.version", "alibabacloud/mopen": "self.version", "alibabacloud/mpserverless": "self.version", "alibabacloud/mts": "self.version", "alibabacloud/multimediaai": "self.version", "alibabacloud/nas": "self.version", "alibabacloud/netana": "self.version", "alibabacloud/nlp": "self.version", "alibabacloud/nlpautoml": "self.version", "alibabacloud/nlscloudmeta": "self.version", "alibabacloud/nlsfiletrans": "self.version", "alibabacloud/objectdet": "self.version", "alibabacloud/ocr": "self.version", "alibabacloud/ocs": "self.version", "alibabacloud/oms": "self.version", "alibabacloud/ons": "self.version", "alibabacloud/onsmqtt": "self.version", "alibabacloud/oos": "self.version", "alibabacloud/openanalytics": "self.version", "alibabacloud/ossadmin": "self.version", "alibabacloud/ots": "self.version", "alibabacloud/outboundbot": "self.version", "alibabacloud/petadata": "self.version", "alibabacloud/polardb": "self.version", "alibabacloud/productcatalog": "self.version", "alibabacloud/pts": "self.version", "alibabacloud/push": "self.version", "alibabacloud/pvtz": "self.version", "alibabacloud/qualitycheck": "self.version", "alibabacloud/ram": "self.version", "alibabacloud/rds": "self.version", "alibabacloud/reid": "self.version", "alibabacloud/retailcloud": "self.version", "alibabacloud/rkvstore": "self.version", "alibabacloud/ros": "self.version", "alibabacloud/rtc": "self.version", "alibabacloud/saf": "self.version", "alibabacloud/sas": "self.version", "alibabacloud/sasapi": "self.version", "alibabacloud/scdn": "self.version", "alibabacloud/schedulerx2": "self.version", "alibabacloud/skyeye": "self.version", "alibabacloud/slb": "self.version", "alibabacloud/smartag": "self.version", "alibabacloud/smc": "self.version", "alibabacloud/sms": "self.version", "alibabacloud/smsintl": "self.version", "alibabacloud/snsuapi": "self.version", "alibabacloud/sts": "self.version", "alibabacloud/taginner": "self.version", "alibabacloud/tesladam": "self.version", "alibabacloud/teslamaxcompute": "self.version", "alibabacloud/teslastream": "self.version", "alibabacloud/ubsms": "self.version", "alibabacloud/ubsmsinner": "self.version", "alibabacloud/uis": "self.version", "alibabacloud/unimkt": "self.version", "alibabacloud/visionai": "self.version", "alibabacloud/vod": "self.version", "alibabacloud/voicenavigator": "self.version", "alibabacloud/vpc": "self.version", "alibabacloud/vs": "self.version", "alibabacloud/wafopenapi": "self.version", "alibabacloud/welfareinner": "self.version", "alibabacloud/xspace": "self.version", "alibabacloud/xtrace": "self.version", "alibabacloud/yqbridge": "self.version", "alibabacloud/yundun": "self.version"}, "require-dev": {"composer/composer": "^1.8", "league/climate": "^3.2.4", "phpunit/phpunit": "^4.8", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud SDK for PHP - Easier to Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "cloud", "library", "sdk"], "time": "2021-04-20T12:56:22+00:00"}, {"name": "alibabacloud/tea", "version": "3.1.21", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "379faffe240ee97134cf3f796cb28059f9fb7fa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/379faffe240ee97134cf3f796cb28059f9fb7fa9", "reference": "379faffe240ee97134cf3f796cb28059f9fb7fa9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "time": "2021-03-15T03:31:41+00:00"}, {"name": "alibabacloud/tea-utils", "version": "0.2.14", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-utils.git", "reference": "381df15cb4bdb58dbf596f94869ffd2ef680eddd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-utils/zipball/381df15cb4bdb58dbf596f94869ffd2ef680eddd", "reference": "381df15cb4bdb58dbf596f94869ffd2ef680eddd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/tea": "^3.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\Utils\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea Utils for PHP", "time": "2021-02-02T10:10:58+00:00"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "e69f57916678458642ac9d2fd341ae78a56996c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/e69f57916678458642ac9d2fd341ae78a56996c8", "reference": "e69f57916678458642ac9d2fd341ae78a56996c8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "~1.0"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "time": "2018-01-08T06:59:35+00:00"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "6ef4c27354368deb2f54b39bbe06601da8c873a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/6ef4c27354368deb2f54b39bbe06601da8c873a0", "reference": "6ef4c27354368deb2f54b39bbe06601da8c873a0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "time": "2019-09-25T22:10:10+00:00"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2017-06-12T01:10:27+00:00"}, {"name": "doctrine/annotations", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "904dca4eb10715b92569fbcd79e201d5c349b6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/904dca4eb10715b92569fbcd79e201d5c349b6bc", "reference": "904dca4eb10715b92569fbcd79e201d5c349b6bc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/lexer": "1.*", "php": "^7.1"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2019-10-01T18:55:10+00:00"}, {"name": "doctrine/lexer", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e17f069ede36f7534b95adec71910ed1b49c74ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e17f069ede36f7534b95adec71910ed1b49c74ea", "reference": "e17f069ede36f7534b95adec71910ed1b49c74ea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-07-30T19:33:28+00:00"}, {"name": "guzzlehttp/command", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/command.git", "reference": "2aaa2521a8f8269d6f5dfc13fe2af12c76921034"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/command/zipball/2aaa2521a8f8269d6f5dfc13fe2af12c76921034", "reference": "2aaa2521a8f8269d6f5dfc13fe2af12c76921034", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.2", "guzzlehttp/promises": "~1.3", "guzzlehttp/psr7": "~1.0", "php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}], "description": "Provides the foundation for building command-based web service clients", "time": "2016-11-24T13:34:15+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.4.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "0895c932405407fd3a7368b6910c09a24d26db11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/0895c932405407fd3a7368b6910c09a24d26db11", "reference": "0895c932405407fd3a7368b6910c09a24d26db11", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2019-10-23T15:58:00+00:00"}, {"name": "guzzlehttp/guzzle-services", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle-services.git", "reference": "9e3abf20161cbf662d616cbb995f2811771759f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle-services/zipball/9e3abf20161cbf662d616cbb995f2811771759f7", "reference": "9e3abf20161cbf662d616cbb995f2811771759f7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/command": "~1.0", "guzzlehttp/guzzle": "^6.2", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/konafets"}], "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "time": "2017-10-06T14:32:02+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "jianyan74/php-excel", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/jianyan74/php-excel.git", "reference": "5b569e16ba35fa48ff7449a7f593172f8284f66b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jianyan74/php-excel/zipball/5b569e16ba35fa48ff7449a7f593172f8284f66b", "reference": "5b569e16ba35fa48ff7449a7f593172f8284f66b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "phpoffice/phpspreadsheet": "^1.3"}, "type": "extension", "autoload": {"psr-4": {"jianyan\\excel\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jianyan74"}], "description": "php excel 导入导出", "keywords": ["csv", "excel", "html", "jianyan74", "xls", "xlsx"], "time": "2020-03-17T03:37:43+00:00"}, {"name": "league/flysystem", "version": "1.0.57", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "0e9db7f0b96b9f12dcf6f65bc34b72b1a30ea55a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/0e9db7f0b96b9f12dcf6f65bc34b72b1a30ea55a", "reference": "0e9db7f0b96b9f12dcf6f65bc34b72b1a30ea55a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.10"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "time": "2019-10-16T21:01:05+00:00"}, {"name": "league/flysystem-cached-adapter", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-cached-adapter.git", "reference": "08ef74e9be88100807a3b92cc9048a312bf01d6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-cached-adapter/zipball/08ef74e9be88100807a3b92cc9048a312bf01d6f", "reference": "08ef74e9be88100807a3b92cc9048a312bf01d6f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"league/flysystem": "~1.0", "psr/cache": "^1.0.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0", "tedivm/stash": "~0.12"}, "suggest": {"ext-phpredis": "Pure C implemented extension for PHP"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Cached\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "frank<PERSON><PERSON>e", "email": "<EMAIL>"}], "description": "An adapter decorator to enable meta-data caching.", "time": "2018-07-09T20:51:04+00:00"}, {"name": "lizhichao/one-sm", "version": "1.9", "source": {"type": "git", "url": "https://github.com/lizhichao/sm.git", "reference": "2e4c57af85ffa763cd0896cc58b5596d2d283c46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lizhichao/sm/zipball/2e4c57af85ffa763cd0896cc58b5596d2d283c46", "reference": "2e4c57af85ffa763cd0896cc58b5596d2d283c46", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"OneSm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "国密sm3", "keywords": ["php", "sm3"], "support": {"issues": "https://github.com/lizhichao/sm/issues", "source": "https://github.com/lizhichao/sm/tree/1.9"}, "time": "2021-02-04T03:28:45+00:00"}, {"name": "markbaker/complex", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/8eaa40cceec7bf0518187530b2e63871be661b72", "reference": "8eaa40cceec7bf0518187530b2e63871be661b72", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2020-03-11T20:15:49+00:00"}, {"name": "markbaker/matrix", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/5348c5a67e3b75cd209d70103f916a93b1f1ed21", "reference": "5348c5a67e3b75cd209d70103f916a93b1f1ed21", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2019-10-06T11:29:25+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "reference": "adcc9531682cf87dfda21e1fd5d0e7a41d292fac", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2016-12-03T22:08:25+00:00"}, {"name": "opis/closure", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "e79f851749c3caa836d7ccc01ede5828feb762c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/e79f851749c3caa836d7ccc01ede5828feb762c7", "reference": "e79f851749c3caa836d7ccc01ede5828feb762c7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "time": "2019-10-19T18:38:51+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2020-04-27T08:12:48+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "446d54b4cb6bf489fc9d75f55843658e6f25d801"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/446d54b4cb6bf489fc9d75f55843658e6f25d801", "reference": "446d54b4cb6bf489fc9d75f55843658e6f25d801", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2019-11-01T11:05:21+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "qcloud/cos-sdk-v5", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/tencentyun/cos-php-sdk-v5.git", "reference": "5dea6bc8be6f8e48fb95a5c4670800d1d796ac42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentyun/cos-php-sdk-v5/zipball/5dea6bc8be6f8e48fb95a5c4670800d1d796ac42", "reference": "5dea6bc8be6f8e48fb95a5c4670800d1d796ac42", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "~6.3", "guzzlehttp/guzzle-services": "~1.1", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Qcloud\\Cos\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "yaozongyou", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud COS", "keywords": ["cos", "php", "qcloud"], "time": "2019-11-07T11:55:10+00:00"}, {"name": "qcloudsms/qcloudsms_php", "version": "v0.1.4", "source": {"type": "git", "url": "https://github.com/qcloudsms/qcloudsms_php.git", "reference": "48822045772d343b93c3d505d8a187cd51153c5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qcloudsms/qcloudsms_php/zipball/48822045772d343b93c3d505d8a187cd51153c5a", "reference": "48822045772d343b93c3d505d8a187cd51153c5a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"sami/sami": "dev-master"}, "type": "library", "autoload": {"psr-4": {"Qcloud\\Sms\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "qcloud sms php sdk", "keywords": ["php", "qcloud", "sdk", "sms"], "support": {"issues": "https://github.com/qcloudsms/qcloudsms_php/issues", "source": "https://github.com/qcloudsms/qcloudsms_php/tree/master"}, "time": "2018-09-19T07:19:17+00:00"}, {"name": "qiniu/php-sdk", "version": "v7.2.10", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "d89987163f560ebf9dfa5bb25de9bd9b1a3b2bd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/d89987163f560ebf9dfa5bb25de9bd9b1a3b2bd8", "reference": "d89987163f560ebf9dfa5bb25de9bd9b1a3b2bd8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "autoload": {"psr-4": {"Qiniu\\": "src/<PERSON>iu"}, "files": ["src/Qiniu/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "time": "2019-10-28T10:23:23+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "singka/singka-sms", "version": "v1.6", "source": {"type": "git", "url": "https://github.com/SingKa-TECH/singka-sms.git", "reference": "8db3543dfb7f28851b94de99dfe9b762f0b9b6a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SingKa-TECH/singka-sms/zipball/8db3543dfb7f28851b94de99dfe9b762f0b9b6a4", "reference": "8db3543dfb7f28851b94de99dfe9b762f0b9b6a4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/client": "^1.5", "guzzlehttp/guzzle": "~6.0@dev", "php": ">=7.0", "qcloudsms/qcloudsms_php": "0.1.*", "qiniu/php-sdk": "^7.2", "singka/ucloud-sms": "^1.8"}, "type": "library", "extra": {"think": {"config": {"sms": "config/config.php"}}}, "autoload": {"psr-4": {"SingKa\\Sms\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "宁波晟嘉网络科技有限公司 夏慧新", "email": "<EMAIL>"}], "description": "适用于ThinkPHP6.0的各种短信接口集成服务，本项目集成了各大云服务厂商的短信业务平台，支持ThinkPHP5.0、ThinkPHP5.1和ThinkPHP6.0，由宁波晟嘉网络科技有限公司维护，目前支持阿里云、腾讯云、七牛云、又拍云、Ucloud和华为云。", "support": {"issues": "https://github.com/SingKa-TECH/singka-sms/issues", "source": "https://github.com/SingKa-TECH/singka-sms/tree/v1.6"}, "time": "2020-07-02T05:07:00+00:00"}, {"name": "singka/ucloud-sms", "version": "v1.8", "source": {"type": "git", "url": "https://github.com/SingKa-TECH/ucloud-sms.git", "reference": "7c4ae42e9c7b26b4db7e6340ff03f68b15e3b0d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SingKa-TECH/ucloud-sms/zipball/7c4ae42e9c7b26b4db7e6340ff03f68b15e3b0d8", "reference": "7c4ae42e9c7b26b4db7e6340ff03f68b15e3b0d8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"think": {"config": {"usms": "config/config.php"}}}, "autoload": {"psr-4": {"Singka\\UcloudSms\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "宁波晟嘉网络科技有限公司 夏慧新", "email": "<EMAIL>"}], "description": "ThinkPHP系列Ucloud的短信接口，支持ThinkPHP6、ThinkPHP5.1和ThinkPHP5.0", "support": {"issues": "https://github.com/SingKa-TECH/ucloud-sms/issues", "source": "https://github.com/SingKa-TECH/ucloud-sms/tree/v1.8"}, "time": "2020-03-24T16:24:09+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.16.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "a54881ec0ab3b2005c406aed0023c062879031e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/a54881ec0ab3b2005c406aed0023c062879031e7", "reference": "a54881ec0ab3b2005c406aed0023c062879031e7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-05-08T16:50:20+00:00"}, {"name": "topthink/framework", "version": "v6.0.0", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "79c555aab0313d1a33ddcdb3c395f2c47f37f597"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/79c555aab0313d1a33ddcdb3c395f2c47f37f597", "reference": "79c555aab0313d1a33ddcdb3c395f2c47f37f597", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-mbstring": "*", "league/flysystem": "^1.0", "league/flysystem-cached-adapter": "^1.0", "opis/closure": "^3.1", "php": ">=7.1.0", "psr/container": "~1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1.1", "topthink/think-orm": "^2.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^7.0"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "time": "2019-10-23T23:28:43+00:00"}, {"name": "topthink/think-captcha", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "0b4305da19e118cefd934007875a8112f9352f01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/0b4305da19e118cefd934007875a8112f9352f01", "reference": "0b4305da19e118cefd934007875a8112f9352f01", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "^6.0.0"}, "type": "library", "extra": {"think": {"services": ["think\\captcha\\CaptchaService"], "config": {"captcha": "src/config.php"}}}, "autoload": {"psr-4": {"think\\captcha\\": "src/"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp", "time": "2019-10-03T07:45:11+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "c28d37743bda4a0455286ca85b17b5791d626e10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/c28d37743bda4a0455286ca85b17b5791d626e10", "reference": "c28d37743bda4a0455286ca85b17b5791d626e10", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "time": "2019-11-08T08:01:10+00:00"}, {"name": "topthink/think-multi-app", "version": "v1.0.11", "source": {"type": "git", "url": "https://github.com/top-think/think-multi-app.git", "reference": "215f4a6bb88e53ad41b448c61957336eb55ce6f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-multi-app/zipball/215f4a6bb88e53ad41b448c61957336eb55ce6f9", "reference": "215f4a6bb88e53ad41b448c61957336eb55ce6f9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0"}, "type": "library", "extra": {"think": {"services": ["think\\app\\Service"]}}, "autoload": {"psr-4": {"think\\app\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp6 multi app support", "time": "2019-10-29T06:34:59+00:00"}, {"name": "topthink/think-orm", "version": "v2.0.27", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "02affaaccade2cdd8bbb2d2f5d15e46113e6eb50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/02affaaccade2cdd8bbb2d2f5d15e46113e6eb50", "reference": "02affaaccade2cdd8bbb2d2f5d15e46113e6eb50", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}, "files": []}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think orm", "keywords": ["database", "orm"], "time": "2019-10-23T02:16:50+00:00"}, {"name": "topthink/think-template", "version": "v2.0.7", "source": {"type": "git", "url": "https://github.com/top-think/think-template.git", "reference": "e98bdbb4a4c94b442f17dfceba81e0134d4fbd19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-template/zipball/e98bdbb4a4c94b442f17dfceba81e0134d4fbd19", "reference": "e98bdbb4a4c94b442f17dfceba81e0134d4fbd19", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "psr/simple-cache": "^1.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "time": "2019-09-20T15:31:04+00:00"}, {"name": "topthink/think-view", "version": "v1.0.13", "source": {"type": "git", "url": "https://github.com/top-think/think-view.git", "reference": "90803b73f781db5d42619082c4597afc58b2d4c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-view/zipball/90803b73f781db5d42619082c4597afc58b2d4c5", "reference": "90803b73f781db5d42619082c4597afc58b2d4c5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/think-template": "^2.0"}, "type": "library", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "time": "2019-10-07T12:23:10+00:00"}, {"name": "topthink/think-worker", "version": "v3.0.5", "source": {"type": "git", "url": "https://github.com/top-think/think-worker.git", "reference": "dc0ac655a0a2efda073ec18e67668bc02d17cabe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-worker/zipball/dc0ac655a0a2efda073ec18e67668bc02d17cabe", "reference": "dc0ac655a0a2efda073ec18e67668bc02d17cabe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "topthink/framework": "^6.0.0", "workerman/gateway-worker": "^3.0.0", "workerman/workerman": "^3.5.23"}, "type": "library", "extra": {"think": {"services": ["think\\worker\\Service"], "config": {"worker": "src/config/worker.php", "worker_server": "src/config/server.php", "gateway_worker": "src/config/gateway.php"}}}, "autoload": {"psr-4": {"think\\worker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "workerman extend for thinkphp6.0", "support": {"issues": "https://github.com/top-think/think-worker/issues", "source": "https://github.com/top-think/think-worker/tree/3.0"}, "time": "2020-04-30T13:39:47+00:00"}, {"name": "workerman/gateway-worker", "version": "v3.0.19", "source": {"type": "git", "url": "https://github.com/walkor/GatewayWorker.git", "reference": "720cb0f23c3ae5f0143b3457901e177dd3d54387"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/GatewayWorker/zipball/720cb0f23c3ae5f0143b3457901e177dd3d54387", "reference": "720cb0f23c3ae5f0143b3457901e177dd3d54387", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"workerman/workerman": ">=3.5.0"}, "type": "library", "autoload": {"psr-4": {"GatewayWorker\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "http://www.workerman.net", "keywords": ["communication", "distributed"], "support": {"issues": "https://github.com/walkor/GatewayWorker/issues", "source": "https://github.com/walkor/GatewayWorker/tree/v3.0.19"}, "time": "2021-01-25T03:01:01+00:00"}, {"name": "workerman/workerman", "version": "v3.5.31", "source": {"type": "git", "url": "https://github.com/walkor/Workerman.git", "reference": "b73ddc45b3c7299f330923a2bde23ca6e974fd96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/Workerman/zipball/b73ddc45b3c7299f330923a2bde23ca6e974fd96", "reference": "b73ddc45b3c7299f330923a2bde23ca6e974fd96", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc.workerman.net/"}, "time": "2020-08-24T03:49:23+00:00"}, {"name": "workerman/workerman-for-win", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/walkor/workerman-for-win.git", "reference": "cbaae3193e4567fd9cfc8099931c63d9b12174ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman-for-win/zipball/cbaae3193e4567fd9cfc8099931c63d9b12174ee", "reference": "cbaae3193e4567fd9cfc8099931c63d9b12174ee", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "type": "project", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc3.workerman.net/index.html"}, "time": "2017-08-28T10:05:00+00:00"}], "packages-dev": [{"name": "eaglewu/swoole-ide-helper", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/wudi/swoole-ide-helper.git", "reference": "fc8fe156391960c75505dd1ef869f52d4e109dc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wudi/swoole-ide-helper/zipball/fc8fe156391960c75505dd1ef869f52d4e109dc6", "reference": "fc8fe156391960c75505dd1ef869f52d4e109dc6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "eagle", "email": "<EMAIL>", "role": "lead"}], "description": "Swoole IDE Helper, to improve auto-completion", "keywords": ["autocomplete", "codeintel", "helper", "ide", "netbeans", "phpdoc", "phpstorm", "sublime", "swoole"], "time": "2020-11-28T15:34:08+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "04ce3335667451138df4307d6a9b61565560199e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/04ce3335667451138df4307d6a9b61565560199e", "reference": "04ce3335667451138df4307d6a9b61565560199e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2019-08-06T08:03:45+00:00"}, {"name": "symfony/var-dumper", "version": "v4.3.6", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "ea4940845535c85ff5c505e13b3205b0076d07bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/ea4940845535c85ff5c505e13b3205b0076d07bf", "reference": "ea4940845535c85ff5c505e13b3205b0076d07bf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "~3.4|~4.0", "symfony/process": "~3.4|~4.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2019-10-13T12:02:04+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"eaglewu/swoole-ide-helper": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.1.0"}, "platform-dev": [], "plugin-api-version": "2.0.0"}