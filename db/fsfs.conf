### This file controls the configuration of the FSFS filesystem.

[memcached-servers]
### These options name memcached servers used to cache internal FSFS
### data.  See http://www.danga.com/memcached/ for more information on
### memcached.  To use memcached with FSFS, run one or more memcached
### servers, and specify each of them as an option like so:
# first-server = 127.0.0.1:11211
# remote-memcached = mymemcached.corp.example.com:11212
### The option name is ignored; the value is of the form HOST:PORT.
### memcached servers can be shared between multiple repositories;
### however, if you do this, you *must* ensure that repositories have
### distinct UUIDs and paths, or else cached data from one repository
### might be used by another accidentally.  Note also that memcached has
### no authentication for reads or writes, so you must ensure that your
### memcached servers are only accessible by trusted users.

[caches]
### When a cache-related error occurs, normally Subversion ignores it
### and continues, logging an error if the server is appropriately
### configured (and ignoring it with file:// access).  To make
### Subversion never ignore cache errors, uncomment this line.
# fail-stop = true

[rep-sharing]
### To conserve space, the filesystem can optionally avoid storing
### duplicate representations.  This comes at a slight cost in
### performance, as maintaining a database of shared representations can
### increase commit times.  The space savings are dependent upon the size
### of the repository, the number of objects it contains and the amount of
### duplication between them, usually a function of the branching and
### merging process.
###
### The following parameter enables rep-sharing in the repository.  It can
### be switched on and off at will, but for best space-saving results
### should be enabled consistently over the life of the repository.
### 'svnadmin verify' will check the rep-cache regardless of this setting.
### rep-sharing is enabled by default.
# enable-rep-sharing = true

[deltification]
### To conserve space, the filesystem stores data as differences against
### existing representations.  This comes at a slight cost in performance,
### as calculating differences can increase commit times.  Reading data
### will also create higher CPU load and the data will be fragmented.
### Since deltification tends to save significant amounts of disk space,
### the overall I/O load can actually be lower.
###
### The options in this section allow for tuning the deltification
### strategy.  Their effects on data size and server performance may vary
### from one repository to another.  Versions prior to 1.8 will ignore
### this section.
###
### The following parameter enables deltification for directories. It can
### be switched on and off at will, but for best space-saving results
### should be enabled consistently over the lifetime of the repository.
### Repositories containing large directories will benefit greatly.
### In rarely accessed repositories, the I/O overhead may be significant
### as caches will most likely be low.
### directory deltification is enabled by default.
# enable-dir-deltification = true
###
### The following parameter enables deltification for properties on files
### and directories.  Overall, this is a minor tuning option but can save
### some disk space if you merge frequently or frequently change node
### properties.  You should not activate this if rep-sharing has been
### disabled because this may result in a net increase in repository size.
### property deltification is enabled by default.
# enable-props-deltification = true
###
### During commit, the server may need to walk the whole change history of
### of a given node to find a suitable deltification base.  This linear
### process can impact commit times, svnadmin load and similar operations.
### This setting limits the depth of the deltification history.  If the
### threshold has been reached, the node will be stored as fulltext and a
### new deltification history begins.
### Note, this is unrelated to svn log.
### Very large values rarely provide significant additional savings but
### can impact performance greatly - in particular if directory
### deltification has been activated.  Very small values may be useful in
### repositories that are dominated by large, changing binaries.
### Should be a power of two minus 1.  A value of 0 will effectively
### disable deltification.
### For 1.8, the default value is 1023; earlier versions have no limit.
# max-deltification-walk = 1023
###
### The skip-delta scheme used by FSFS tends to repeatably store redundant
### delta information where a simple delta against the latest version is
### often smaller.  By default, 1.8+ will therefore use skip deltas only
### after the linear chain of deltas has grown beyond the threshold
### specified by this setting.
### Values up to 64 can result in some reduction in repository size for
### the cost of quickly increasing I/O and CPU costs. Similarly, smaller
### numbers can reduce those costs at the cost of more disk space.  For
### rarely read repositories or those containing larger binaries, this may
### present a better trade-off.
### Should be a power of two.  A value of 1 or smaller will cause the
### exclusive use of skip-deltas (as in pre-1.8).
### For 1.8, the default value is 16; earlier versions use 1.
# max-linear-deltification = 16
###
### After deltification, we compress the data to minimize on-disk size.
### This setting controls the compression algorithm, which will be used in
### future revisions.  It can be used to either disable compression or to
### select between available algorithms (zlib, lz4).  zlib is a general-
### purpose compression algorithm.  lz4 is a fast compression algorithm
### which should be preferred for repositories with large and, possibly,
### incompressible files.  Note that the compression ratio of lz4 is
### usually lower than the one provided by zlib, but using it can
### significantly speed up commits as well as reading the data.
### lz4 compression algorithm is supported, starting from format 8
### repositories, available in Subversion 1.10 and higher.
### The syntax of this option is:
###   compression = none | lz4 | zlib | zlib-1 ... zlib-9
### Versions prior to Subversion 1.10 will ignore this option.
### The default value is 'lz4' if supported by the repository format and
### 'zlib' otherwise.  'zlib' is currently equivalent to 'zlib-5'.
# compression = lz4
###
### DEPRECATED: The new 'compression' option deprecates previously used
### 'compression-level' option, which was used to configure zlib compression.
### For compatibility with previous versions of Subversion, this option can
### still be used (and it will result in zlib compression with the
### corresponding compression level).
###   compression-level = 0 ... 9 (default is 5)

[packed-revprops]
### This parameter controls the size (in kBytes) of packed revprop files.
### Revprops of consecutive revisions will be concatenated into a single
### file up to but not exceeding the threshold given here.  However, each
### pack file may be much smaller and revprops of a single revision may be
### much larger than the limit set here.  The threshold will be applied
### before optional compression takes place.
### Large values will reduce disk space usage at the expense of increased
### latency and CPU usage reading and changing individual revprops.
### Values smaller than 4 kByte will not improve latency any further and 
### quickly render revprop packing ineffective.
### revprop-pack-size is 16 kBytes by default for non-compressed revprop
### pack files and 64 kBytes when compression has been enabled.
# revprop-pack-size = 16
###
### To save disk space, packed revprop files may be compressed.  Standard
### revprops tend to allow for very effective compression.  Reading and
### even more so writing, become significantly more CPU intensive.
### Compressing packed revprops is disabled by default.
# compress-packed-revprops = false

[io]
### Parameters in this section control the data access granularity in
### format 7 repositories and later.  The defaults should translate into
### decent performance over a wide range of setups.
###
### When a specific piece of information needs to be read from disk,  a
### data block is being read at once and its contents are being cached.
### If the repository is being stored on a RAID, the block size should be
### either 50% or 100% of RAID block size / granularity.  Also, your file
### system blocks/clusters should be properly aligned and sized.  In that
### setup, each access will hit only one disk (minimizes I/O load) but
### uses all the data provided by the disk in a single access.
### For SSD-based storage systems, slightly lower values around 16 kB
### may improve latency while still maximizing throughput.  If block-read
### has not been enabled, this will be capped to 4 kBytes.
### Can be changed at any time but must be a power of 2.
### block-size is given in kBytes and with a default of 64 kBytes.
# block-size = 64
###
### The log-to-phys index maps data item numbers to offsets within the
### rev or pack file.  This index is organized in pages of a fixed maximum
### capacity.  To access an item, the page table and the respective page
### must be read.
### This parameter only affects revisions with thousands of changed paths.
### If you have several extremely large revisions (~1 mio changes), think
### about increasing this setting.  Reducing the value will rarely result
### in a net speedup.
### This is an expert setting.  Must be a power of 2.
### l2p-page-size is 8192 entries by default.
# l2p-page-size = 8192
###
### The phys-to-log index maps positions within the rev or pack file to
### to data items,  i.e. describes what piece of information is being
### stored at any particular offset.  The index describes the rev file
### in chunks (pages) and keeps a global list of all those pages.  Large
### pages mean a shorter page table but a larger per-page description of
### data items in it.  The latency sweetspot depends on the change size
### distribution but covers a relatively wide range.
### If the repository contains very large files,  i.e. individual changes
### of tens of MB each,  increasing the page size will shorten the index
### file at the expense of a slightly increased latency in sections with
### smaller changes.
### For source code repositories, this should be about 16x the block-size.
### Must be a power of 2.
### p2l-page-size is given in kBytes and with a default of 1024 kBytes.
# p2l-page-size = 1024

[debug]
###
### Whether to verify each new revision immediately before finalizing
### the commit.  This is disabled by default except in maintainer-mode
### builds.
# verify-before-commit = false
