.lay-step {
    font-size: 0;
    width: 400px;
    margin: 0 auto;
    max-width: 100%;
    padding-left: 200px;
}

.step-item {
    display: inline-block;
    line-height: 26px;
    position: relative;
    font-size: 14px;
}

.step-item-tail {
    width: 100%;
    padding: 0 10px;
    position: absolute;
    left: 0;
    top: 13px;
}

.step-item-tail i {
    display: inline-block;
    width: 100%;
    height: 1px;
    vertical-align: top;
    background: #c2c2c2;
    position: relative;
}

.step-item-tail .step-item-tail-done {
    background: #009688;
}

.step-item-head {
    position: relative;
    display: inline-block;
    height: 26px;
    width: 26px;
    text-align: center;
    vertical-align: top;
    color: #009688;
    border: 1px solid #009688;
    border-radius: 50%;
    background: #ffffff;
}

.step-item-head.step-item-head-active {
    background: #009688;
    color: #ffffff;
}

.step-item-main {
    display: block;
    position: relative;
    margin-left: -50%;
    margin-right: 50%;
    padding-left: 26px;
    text-align: center;
}

.step-item-main-title {
    font-weight: bolder;
    color: #555555;
}

.step-item-main-desc {
    color: #aaaaaa;
}

.lay-step + [carousel-item]:before {
    display: none;
}

.lay-step + [carousel-item] > * {
    background-color: transparent;
}