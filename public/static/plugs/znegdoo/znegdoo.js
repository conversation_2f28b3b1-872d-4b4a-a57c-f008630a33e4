define(["jquery"], function ($) {
    var form = layui.form,
        layer = layui.layer;
    var zn_admin = {
        //厂区鸡舍联动，获取生产鸡舍数据
        getHouseProd: function (val) {
            $.ajax({
                url: '../Common/getHouseProd',
                data: {
                    factory_id: val
                },
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    console.log(res);
                    $('#prod_id').empty();
                    $('#prod_id').append('<option style="display:none"></option>');
                    res.forEach(function (e) {
                        $('#prod_id').append('<option value="' + e.id + '">' + e.housename + '</option>');
                    })
                    form.render();
                }
            })
        },

        //厂区与仓库联动，获取所在厂区仓库,不根据权限
        getWarehouse: function (val, type = '') {
            $.ajax({
                url: '../Common/wareHouse',
                data: {
                    factory_id: val,
                    type: type,
                    isRange: 0
                },
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    console.log(res);
                    $('#warehouse_id').empty();
                    $('#warehouse_id').append('<option style="display:none"></option>');
                    res.data.forEach(function (e) {
                        $('#warehouse_id').append('<option value="' + e.id + '">' + e.name + '</option>');
                    })
                    form.render();
                }
            })
        },
        //获取工厂数据
        getFactory: function (type = '') {
            var data1;
            $.ajax({
                url: '../Common/factoryList',
                data: {
                    type: type,//厂区类型
                },
                async: false,
                success: function (data) {
                    data1 = data.data;
                }
            });
            let liststr = '{';
            data1.forEach(function (item) {
                liststr += '"' + item.id + '":"' + item.factoryname + '",';
            })
            liststr = liststr.substring(0, liststr.length - 1);
            liststr += '}';
            const list = JSON.parse(liststr);
            return list;
        },
        //获取鸡舍数据
        getHouseList: function () {
            var data1;
            $.ajax({
                url: '../Common/getHouseProd',
                async: false,
                success: function (data) {
                    data1 = data;
                }
            });
            let liststr = '';
            let list = {};
            if (data1.length==0){
               liststr='{}';
            }else{
                liststr = '{';
                data1.forEach(function (item) {
                    liststr += '"' + item.id + '":"' + item.housename + '",';
                })
                if(liststr.length>1){
                    liststr = liststr.substring(0, liststr.length - 1);
                }
                liststr += '}';
                list = JSON.parse(liststr);
            }
            return list;
        },
        //获取员工数据
        getWorker: function () {
            var data1;
            $.ajax({
                url: '../Common/workerList',
                async: false,
                success: function (data) {
                    data1 = data.data;
                }
            });
            let liststr = '{';
            data1.forEach(function (item) {
                liststr += '"' + item.id + '":"' + item.name + '",';
            })
            liststr = liststr.substring(0, liststr.length - 1);
            liststr += '}';
            return JSON.parse(liststr);
        },
        //获取品种数据
        getHensType: function () {
            var data1;
            $.ajax({
                url: '../Common/henTypeList',
                async: false,
                success: function (data) {
                    data1 = data.data;
                }
            });
            let liststr = '{';
            data1.forEach(function (item) {
                liststr += '"' + item.id + '":"' + item.typename + '",';
            })
            liststr = liststr.substring(0, liststr.length - 1);
            liststr += '}';
            const list = JSON.parse(liststr);
            return list;
        },
        //获取物料数据
        getSundriesList: function(){
            var data1;
            var list = {};
            $.ajax({
                url: '../Common/sundriesList',
                async: false,
                success: function (data) {
                    data1 = data.data;
                    data1.forEach(function (item,index) {
                        list[item.id]=item.name;
                    })
                }
            });
            return list;
        },
        //获取仓库数据列表
        getWarehouseList: function(type=''){
            var data1;
            var list = {};
            $.ajax({
                url: '../Common/wareHouse',
                data:{
                    type:type
                },
                async: false,
                success: function (data) {
                    data1 = data.data;
                    data1.forEach(function (item,index) {
                        list[item.id]=item.name;
                    })
                }
            });
            return list;
        },
        //获取药品数据列表
        getDrugsList: function(){
            var data1;
            $.ajax({
                url: '../Common/getDrugsList',
                async: false,
                success: function (data) {
                    data1 = data.data;
                }
            });
            let liststr = '{';
            data1.forEach(function (item) {
                liststr += '"' + item.id + '":"' + item.drugsname + '",';
            })
            liststr = liststr.substring(0, liststr.length - 1);
            liststr += '}';
            return JSON.parse(liststr);
        },
        //回车后焦点到下一个input
        focusNextInput: function (thisInput) {
            var inputs = document.getElementsByTagName("input");
            for (var i = 0; i < inputs.length; i++) {
                // 如果是最后一个，则焦点回到第一个
                if (i == (inputs.length - 1)) {
                    inputs[0].focus();
                    break;
                } else if (thisInput == inputs[i]) {
                    inputs[i + 1].focus();
                    break; //不加最后一行eles就直接回到第一个输入框
                }
            }

        },
        //计算单价
        resultPrice: function () {
            $('#amount').blur(function (data) {
                var price = Number($('#amount').val() / $('#num').val()).toFixed(2);
                $('#price').val(price);
            })
            $('#num').blur(function (data) {
                var price = Number($('#amount').val() / $('#num').val()).toFixed(2);
                $('#price').val(price);
            })
        },

        //执行搜索-明细查询
        doSearch: function (cols) {
            // 加载表格
            layui.use(['table', 'jquery', 'layer'], function () {
                var table = layui.table;
                var $ = layui.jquery;
                table.render({
                    // 表格容器ID
                    elem: '#searchTable'
                    ,totalRow: true // 开启合计行
                    // 表格ID，必须设置，重载部分需要用到
                    , id: 'searchTable_filter'
                    // 数据接口链接
                    , url: "search"
                    // 附加参数，post token
                    //, where: {'token': token}
                    , method: 'post'
                    // 分页 curr起始页，groups连续显示的页码，limit默认每页显示的条数
                    , page: {
                        layout: ['prev', 'page', 'next', 'skip', 'count', 'limit']
                        , curr: 1
                        , groups: 6
                        , limit: 20
                    }
                    , cols: cols
                });

                // 执行搜索，表格重载

                $('#do_search').on('click', function () {
                    // 搜索条件
                    var whereJson = '';
                    var opJson = '';
                    var typeJson = '';
                    var where = {};
                    var op = {};
                    var type = {};
                    $('form').each(function () {
                        let w = {};
                        //遍历form中的元素
                        $('input, select, textarea', this).each(function () {
                            // 这里你可以对每个输入元素进行操作
                            //console.log(this.name, ':', $(this).val());
                            var opValue = $(this).attr('data-op');//操作符
                            var typeValue = $(this).attr('data-type');//区分主表与副表

                            if (this.name != '') {
                                where[this.name] = $(this).val();
                                op[this.name] = opValue;
                                type[this.name] = typeValue;
                            }

                        });
                    });

                    whereJson = JSON.stringify(where);
                    opJson = JSON.stringify(op);
                    typeJson = JSON.stringify(type);
                    // console.log(opJson);
                    // console.log(whereJson);
                    // 执行搜索，表格重载
                    table.reload('searchTable_filter', {
                        method: 'post'
                        , where: {
                            'where': whereJson,
                            'op': opJson,
                            'type': typeJson
                        }
                        , page: {
                            curr: 1,
                        }
                    });
                });

                // 执行重置，表格重载

                $('#do_reset').on('click', function () {
                    // 搜索条件
                    table.reload('searchTable_filter', {
                        method: 'post'
                        , where: {
                            'where': '',
                        }
                        , page: {
                            curr: 1,
                        }
                    });
                });
            })
        },
        //动态复制select
        createSelect:function(sourceid,selectid) {
            var source = document.getElementById(sourceid);
            var select = document.getElementById(selectid);
            var options = [];
            for (var i = 0; i < source.options.length; i++) {
                var value = source.options[i].value;
                var text = source.options[i].text;
                options[i] = document.createElement('option');
                options[i].value = value;
                options[i].text = text;
                select.appendChild(options[i]);
            }
            // 将select添加到div容器
            //container.appendChild(select);
        },
        // 厂区选项卡
        createFactoryTab:function(key){
            layui.use(['table', 'jquery', 'layer'], function () {
                $('#lbh-tab').on('click', function (data) {
                    var table = layui.table;
                    var target = $(data.target); // 获取实际点击的子元素
                    // 判断具体是哪个子元素被点击
                    if (target.hasClass('lbh-factory')) {
                        var isid = target.attr('id');
                        var idname = 'div-' + isid;
                        // 使用jQuery选择器设置样式
                        $('div.lbh-myDiv').css("border-bottom", "none");
                        $('.lbh-factory').css("color", "#666666");
                        $('#' + idname).css("border-bottom", "2px solid #5fb878");
                        $('#' + isid).css("color", "#299688");
                        var id = target.data('value');
                        var formatFilter = {},
                            formatOp = {};
                        formatFilter[key] = id;
                        formatOp[key] =  '=';
                        table.reload('currentTableRenderId', {
                            page: {
                                curr: 1
                            }
                            , where: {
                                filter: JSON.stringify(formatFilter),
                                op: JSON.stringify(formatOp)
                            }
                        }, 'data');


                        return false;
                    }

                });
            })
        },
        mytemp: function () {
            return "abc";
        },
    };

    //触发INPUT回车
    $(document).on('keydown', 'input[type = text]', function (event) {
        if (event.keyCode == 13) {
            zn_admin.focusNextInput(this);
            event.preventDefault();
        }
    });
    return zn_admin;
});