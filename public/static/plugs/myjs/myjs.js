//厂区鸡舍联动，获取生产鸡舍数据
function getHouseProd (val, form) {
    $.ajax({
        url: '../Common/getHouseProd',
        data: {
            factory_id: val
        },
        type: 'get',
        dataType: 'json',
        success: function (res) {
            console.log(res);
            $('#prod_id').empty();
            $('#prod_id').append('<option style="display:none"></option>');
            res.forEach(function (e) {
                $('#prod_id').append('<option value="' + e.id + '">' + e.housename + '</option>');
            })
            form.render();
        }
    })
}

//计算单价
function resultPrice() {
    $('#amount').blur(function (data) {
        var price = Number($('#amount').val() / $('#num').val()).toFixed(2);
        $('#price').val(price);
    })
    $('#num').blur(function (data) {
        var price = Number($('#amount').val() / $('#num').val()).toFixed(2);
        $('#price').val(price);
    })
}
//获取批次列表
function getBatch(){
    var data1;
    $.ajax({
        url: '../Common/batchList',
        async: false,
        success: function (data) {
            data1 = data.data;
        }
    });
    let liststr = '{';
    data1.forEach(function (item) {
        liststr += '"' + item.id + '":"' + item.batchname + '",';
    })
    liststr = liststr.substring(0, liststr.length - 1);
    liststr += '}';
    const list = JSON.parse(liststr);
    return list;
}

//获取工厂数据
function getFactory(){
    var data1;
    $.ajax({
        url: '../Common/factoryList',
        async: false,
        success: function (data) {
            data1 = data.data;
        }
    });
    let liststr = '{';
    data1.forEach(function (item) {
        liststr += '"' + item.id + '":"' + item.factoryname + '",';
    })
    liststr = liststr.substring(0, liststr.length - 1);
    liststr += '}';
    const list = JSON.parse(liststr);
    return list;
}
//获取员工数据
function getWorker(){
    var data1;
    $.ajax({
        url: '../Common/workerList',
        async: false,
        success: function (data) {
            data1 = data.data;
        }
    });
    let liststr = '{';
    data1.forEach(function (item) {
        liststr += '"' + item.id + '":"' + item.name + '",';
    })
    liststr = liststr.substring(0, liststr.length - 1);
    liststr += '}';
    const list = JSON.parse(liststr);
    return list;
}
//获取品种数据
function getHensType(){
    var data1;
    $.ajax({
        url: '../Common/henTypeList',
        async: false,
        success: function (data) {
            data1 = data.data;
        }
    });
    let liststr = '{';
    data1.forEach(function (item) {
        liststr += '"' + item.id + '":"' + item.typename + '",';
    })
    liststr = liststr.substring(0, liststr.length - 1);
    liststr += '}';
    const list = JSON.parse(liststr);
    return list;
}

//执行搜索
function doSearch(cols){
    // 加载表格
    layui.use(['table','jquery','layer'], function () {
        var table = layui.table;
        var $ = layui.jquery;
        table.render({
            // 表格容器ID
            elem: '#searchTable'
            // 表格ID，必须设置，重载部分需要用到
            , id: 'searchTable_filter'
            // 数据接口链接
            , url: "search"
            // 附加参数，post token
            //, where: {'token': token}
            , method: 'post'
            // 分页 curr起始页，groups连续显示的页码，limit默认每页显示的条数
            , page: {
                layout: ['prev', 'page', 'next', 'skip' , 'count','limit' ]
                , curr: 1
                , groups: 6
                , limit: 20
            }
            , cols: cols
        });

        // 执行搜索，表格重载

        $('#do_search').on('click', function () {
            // 搜索条件
            var whereJson='';
            var opJson='';
            var typeJson='';
            var where= {};
            var op={};
            var type={};
            $('form').each(function() {
                let w={};
                //遍历form中的元素
                $('input, select, textarea', this).each(function() {
                    // 这里你可以对每个输入元素进行操作
                    //console.log(this.name, ':', $(this).val());
                    var opValue = $(this).attr('data-op');//操作符
                    var typeValue = $(this).attr('data-type');//区分主表与副表

                    if(this.name!=''){
                        where[this.name]=$(this).val();
                        op[this.name]=opValue;
                        type[this.name]=typeValue;
                    }

                });
            });

            whereJson=JSON.stringify(where);
            opJson=JSON.stringify(op);
            typeJson=JSON.stringify(type);
            // console.log(opJson);
            // console.log(whereJson);
            // 执行搜索，表格重载
            table.reload('searchTable_filter', {
                method: 'post'
                , where: {
                    'where':whereJson,
                    'op':opJson,
                    'type':typeJson
                }
                , page: {
                    curr: 1,
                }
            });
        });

        // 执行重置，表格重载

        $('#do_reset').on('click', function () {
            // 搜索条件
            table.reload('searchTable_filter', {
                method: 'post'
                , where: {
                    'where':'',
                }
                , page: {
                    curr: 1,
                }
            });
        });
    })
}