define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundrieslist/index',
        add_url: 'goods.sundrieslist/add',
        edit_url: 'goods.sundrieslist/edit',
        delete_url: 'goods.sundrieslist/delete',
        export_url: 'goods.sundrieslist/export',
        modify_url: 'goods.sundrieslist/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'name', title: '物品名称'},
                    {field: 'brand', title: '品牌',search:false},
                    {field: 'munit', title: '计量单位',search:false},
                    // {field: 'pack', title: '规格包装'},
                    // {field: 'unitname', title: '生产厂家'},
                    {field: 'remark', title: '说明', templet: ea.table.text,search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});