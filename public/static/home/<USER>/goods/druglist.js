define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.druglist/index',
        add_url: 'goods.druglist/add',
        edit_url: 'goods.druglist/edit',
        delete_url: 'goods.druglist/delete',
        export_url: 'goods.druglist/export',
        modify_url: 'goods.druglist/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'drugsname', title: '药品名称'},
                    {field: 'brand', title: '品牌'},
                    {field: 'appcode', title: '批准文号'},
                    {field: 'munit', title: '计量单位',search:false},
                    {field: 'pack', title: '包装规格（如2板，每板12粒）',search:false},
                    {field: 'stock_warn', title: '警戒库存',search:false},
                    {field: 'unitname', title: '生产厂家',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],

            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});