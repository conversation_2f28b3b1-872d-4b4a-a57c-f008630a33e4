define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundries2return/index',
        add_url: 'goods.sundries2return/add',
        edit_url: 'goods.sundries2return/edit',
        delete_url: 'goods.sundries2return/delete',
        export_url: 'goods.sundries2return/export',
        modify_url: 'goods.sundries2return/modify',
    };

    var Controller = {

        index: function () {
            var sundriesList = zn.getSundriesList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'cdate', title: '日期',search:'range'},
                    {field: 'sundries_id', minWidth:80,title: '物料名称',search:"select",selectList:sundriesList,templet: function (d){
                            return d.name;
                        }},
                    {field: 'rele_no', title: '关联单号',search:false},
                    {field: 'num', title: '数量',search:false},
                    {field: 'price', title: '单价',search:false},
                    {field: 'amount', title: '总价',search:false},
                    {field: 'factoryname', title: '厂区',search:false},
                    {field: 'custom_name', title: '客户',search:false},
                    {field: 'note', title: '说明',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };
    return Controller;
});