define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundriesreceive/index',
        add_url: 'goods.sundriesreceive/add',
        edit_url: 'goods.sundriesreceive/edit',
        delete_url: 'goods.sundriesreceive/delete',
        export_url: 'goods.sundriesreceive/export',
        modify_url: 'goods.sundriesreceive/modify',
        print_url: 'goods.sundriesreceive/print',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '领取',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    },{
                        text: '打印月报表',
                        url: init.print_url,
                        method: 'open',
                        auth: 'edit',
                        class: 'layui-btn layui-btn-warm layui-btn-sm',
                        icon: 'fa fa-print ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sundriesList.name', title: '物品名称'},
                    {field: 'receive_date', title: '领取时间',search:'range'},
                    {field: 'num',width:80, title: '数量',search:false},
                    {field: 'sundriesList.munit', width:80,title: '单位',search:false},
                    {field: 'workerList.name', title: '领料员工',search:false},
                    {field: 'housename', title: '领取鸡舍'},
                    {field: 'factory.factoryname', title: '厂区',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            form.on('select(houseid)',function (data){
                setBatch(data.value);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_date));
                data.receive_date=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            form.on('select(houseid)',function (data){
                setBatch(data.value);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_date));
                data.receive_date=date.getTime()/1000;
                return data;
            });
        },
        print:function (){
            ea.listen();
        }
    };
    //设置batch_id值
    function setBatch(houseid){
        $.ajax({
            url:'../Common/getBatch',
            data:{houseid:houseid},
            type:'get',
            success:function(res){
                let batch_id=res.data;
                $('#batch_id').val(batch_id);
            },
            error:function () {
                console.log("出错了");
            },//表示如果请求响应出现错误，会执行的回调函数
            dataType:"json" //设置接受到的响应数据的格式
        })
    }
    return Controller;
});