define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.feedendtobuy/index',
        add_url: 'goods.feedendtobuy/add',
        edit_url: 'goods.feedendtobuy/edit',
        delete_url: 'goods.feedendtobuy/delete',
        export_url: 'goods.feedendtobuy/export',
        modify_url: 'goods.feedendtobuy/modify',
        print_url: 'goods.feedendtobuy/print',
        search_url: 'goods.feedendtobuy/search',
    };
    var index = Number($('#index').val());
    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh',
                    [{
                        text: '采购',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '入库单号',search:false},
                    {field: 'feedname', title: '饲料名称',search:false},
                    {field: 'num', title: '数量',search:false},
                    {field: 'cdate', title: '购买日期',search:false},
                    {field: 'amount', title: '总价', search: false, templet: function(d){
                        return d.amount==null?'':('￥'+d.amount);
                        }},
                    // {field: 'wareHouse_id', title: '仓库',search: false,templet:function (d){
                    //         return d.warehouse_id==null?'':d.wareHouse.name;
                    //     }},
                    {field: 'factoryIn.factoryname', title: '厂区',search: false},
                    {field: 'supperList.comname', title: '供应商',search: false,templet: function (d){
                        return d.supper_id==null?'':d.supperList.comname;
                        }},
                    {field: 'source', title: '来源',selectList:{0:'采购',1:'领取'},search:false},
                    {width: 250, title: '操作', templet: ea.table.tool, operat: [[
                            {
                                text: '打印入库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });

        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },

        search:function(){
            var cols=[[
                {field: 'no', title: '入库单号',align:"center"},
                {field: 'feedName', title: '饲料名称',align:"center"},
                {field: 'num', title: '数量(公斤)', align:"center"},
                {field: 'cdate', title: '购买日期', align:"center"},
                {field: 'price', title: '单价', align:"center",templet:function(d){
                        if(d.price===null){return ''}else{return '￥'+d.price}
                    }},
                {field: 'amount', title: '金额', align:"center",templet:function(d){
                        if(d.amount===null){return ''}else{return '￥'+d.amount}
                    }},
                {field: 'supperName', title: '供应商', align:"center"},
                {field: 'factoryName', title: '厂区', align:"center"},
                {field: 'sourcename', title: '来源', align:"center"},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },
    };
    var form = layui.form;
    $('#add').on('click', function () {
        index++;
        var selectid = 'feedend' + index;
        var div_id = 'div' + index;
        var num_id = 'num' + index;
        var amount_id = 'amount' + index;
        var price_id = 'price' + index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id=""><select name="feedend_id[]" id="' + selectid + '" lay-verify="required" lay-search></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="' + num_id + '" placeholder="请输入数量"  onblur="setTotal(' + index + ')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="' + price_id + '" placeholder="请输入价格" onblur="setTotal(' + index + ')"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="' + amount_id + '" value="" placeholder="请输入金额"  onblur="setTotal(' + index + ')"/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect(selectid);
        form.render('select');
    })

    function createSelect(selectid) {
        var source = document.getElementById('feedend1');
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }
    return Controller;
});