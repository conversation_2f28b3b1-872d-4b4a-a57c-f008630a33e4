define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.feedendtolist/index',
        add_url: 'goods.feedendtolist/add',
        edit_url: 'goods.feedendtolist/edit',
        delete_url: 'goods.feedendtolist/delete',
        export_url: 'goods.feedendtolist/export',
        modify_url: 'goods.feedendtolist/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    //{field: 'id', title: '编号'},
                    {field: 'feedname', title: '饲料名称'},
                    {field: 'brand', title: '品牌',search:false},
                    {field: 'stock_warn', title: '警戒库存(Kg)',search:false},
                    // {field: 'stage', title: '适用生长期',selectList:{1:'育雏期',2:'育成期',3:'产蛋期',4:'产蛋高峰期'}},
                    {field: 'status', title: '状态', width: 85, search: 'select', selectList: {0: '禁用', 1: '启用'}, templet: ea.table.switch},
                    {field: 'unitname', title: '生产厂家',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});