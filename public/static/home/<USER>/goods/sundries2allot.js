define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundries2allot/index',
        add_url: 'goods.sundries2allot/add',
        edit_url: 'goods.sundries2allot/edit',
        delete_url: 'goods.sundries2allot/delete',
        export_url: 'goods.sundries2allot/export',
        modify_url: 'goods.sundries2allot/modify',
    };

    var Controller = {

        index: function () {
            var sundriesList = zn.getSundriesList();
            var factoryList = zn.getFactory();
            console.log(sundriesList);
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'cdate', title: '日期',search:'range'},
                    {field: 'sundries_id', minWidth:80,title: '物料名称',search:"select",selectList:sundriesList,templet: function (d){
                            return d.name;
                        }},
                    {field: 'num', title: '数量',search:false},
                    {field: 'factory_out_id', minWidth:80,title: '调出单位',search:"select",selectList:factoryList,templet: function (d){
                            return d.source_name;
                        }},
                    {field: 'factory_in_id', minWidth:80,title: '调入单位',search:"select",selectList:factoryList,templet: function (d){
                            return d.target_name;
                        }},
                    {field: 'note', title: '说明',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };
    return Controller;
});