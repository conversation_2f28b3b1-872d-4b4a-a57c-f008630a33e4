define(["jquery", "easy-admin" ,"znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.drugstock/index',
        add_url: 'goods.drugstock/add',
        edit_url: 'goods.drugstock/edit',
        delete_url: 'goods.drugstock/delete',
        export_url: 'goods.drugstock/export',
        modify_url: 'goods.drugstock/modify',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var drugsList = zn.getDrugsList();
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区',selectList:factoryList},
                    {field: 'appcode', title: '批准文号',width:120,search: false},
                    {field: 'drugs_id', title: '药品名称',selectList:drugsList,templet:function (d){
                        return d.drugs_name;
                        }},
                    {field: 'num', title: '采购数量',search: false},
                    {field: 'allot_in', title: '调拨入库',search: false},
                    {field: 'receive', title: '领用数量',search: false},
                    {field: 'allot_out', title: '调拨出库',search: false},
                    {field: 'stock_num', title: '库存数量',templet:function (d){
                            if(d.stock_num<d.stock_warn){
                                return "<span style='color:red'>"+d.stock_num+" ↓</span>";
                            }else{
                                return d.stock_num;
                            }
                        },search: false},
                    {field: 'stock_warn', title: '警戒库存',search: false},
                    {field: 'munit', title: '计量单位',search: false},
                    {field: 'unitname', title: '生产厂家',search: false},
                ]],
            });

            ea.listen();
        },
        add: function () {
                       ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});