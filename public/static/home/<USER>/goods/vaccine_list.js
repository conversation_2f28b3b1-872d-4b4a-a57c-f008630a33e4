define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.vaccine_list/index',
        add_url: 'goods.vaccine_list/add',
        edit_url: 'goods.vaccine_list/edit',
        delete_url: 'goods.vaccine_list/delete',
        export_url: 'goods.vaccine_list/export',
        modify_url: 'goods.vaccine_list/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'name', title: '名称'},
                    {field: 'brand', title: '品牌'},
                    {field: 'appcode', title: '批准文号',search:false},
                    {field: 'munit', title: '计量单位',search:false},
                    {field: 'pack', title: '包装规格（如2板，每板12粒）',search:false},
                    {field: 'stock_warn', title: '警戒库存',search:false},
                    {field: 'unitname', title: '生产厂家'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});