define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sunreturn/index',
        add_url: 'goods.sunreturn/add',
        edit_url: 'goods.sunreturn/edit',
        delete_url: 'goods.sunreturn/delete',
        export_url: 'goods.sunreturn/export',
        modify_url: 'goods.sunreturn/modify',
    };

    var Controller = {

        index: function () {
            var sundriesList = zn.getSundriesList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'custom_name', title: '客户',search:false},
                    {field: 'cdate', title: '日期',search:'range'},
                    {field: 'sundries_id', minWidth:80,title: '物料名称',search:"select",selectList:sundriesList,templet: function (d){
                            return d.name;
                        }},
                    {field: 'num', title: '数量',search:false},
                    {field: 'note', title: '说明',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };
    return Controller;
});