define(["jquery", "easy-admin","znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.drugstobuy/index',
        add_url: 'goods.drugstobuy/add',
        edit_url: 'goods.drugstobuy/edit',
        delete_url: 'goods.drugstobuy/delete',
        export_url: 'goods.drugstobuy/export',
        modify_url: 'goods.drugstobuy/modify',
        print_url: 'goods.drugstobuy/print',
        search_url: 'goods.drugstobuy/search',
    };
    var index = Number($('#index').val());
    var Controller = {
        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh',
                    [{
                        text: '采购',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                            text: '明细查询',
                            url: init.search_url,
                            method: 'open',
                            auth: 'add',
                            class: 'layui-btn layui-bg-orange layui-btn-sm',
                            icon: 'fa fa-search ',
                            extend: 'data-full="true"',
                        }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '入库单号',search: false},
                    {field: 'cdate', title: '购入日期',search:'range'},
                    {field: 'factory.factoryname', title: '厂区',search: false},
                    {field: 'supper.comname', title: '供应商',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '打印入库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getWarehouse(data.value,3);//获取药品仓库
            })
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        search:function(){
            var cols=[[
                {field: 'no', title: '入库单号',align:"center"},
                {field: 'drugsName', title: '药品名称',align:"center"},
                {field: 'appcode', title: '批准文号',align:"center"},
                {field: 'pid', title: '生产批号', align:"center"},
                {field: 'edate', title: '有效期', align:"center"},
                {field: 'cdate', title: '购买日期', align:"center"},
                {field: 'num', title: '数量', align:"center"},
                {field: 'munit', title: '单位', align:"center"},
                {field: 'price', title: '单价', align:"center",templet:function(d){
                        if(d.price===null){return ''}else{return '￥'+d.price}
                    }},
                {field: 'amount', title: '金额', align:"center",templet:function(d){
                        if(d.amount===null){return ''}else{return '￥'+d.amount}
                    }},
                {field: 'supperName', title: '供应商', align:"center"},
                {field: 'warehouseName', title: '仓库', align:"center"},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },
    };
    var form = layui.form;
    $('#add').on('click', function () {
        index++;
        var selectid = 'drugs_id' + index;
        var div_id = 'div' + index;
        var num_id = 'num' + index;
        var edate = 'edate' + index;
        var pid = 'pid' + index;
        var munit = 'munit' + index;
        var amount_id = 'amount' + index;
        var price_id = 'price' + index;
        var edateid = 'edateid' + index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id=""><select name="drugs_id[]" id="' + selectid + '" lay-verify="required" lay-search></select></td>' +
            '<td><input type="text" class="layui-input" name="pid[]" id="' + pid + '" placeholder="请输入生产批号"  value=""/></td>' +
            '<td id="'+edateid+'"></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="' + num_id + '" placeholder="请输入数量"  onblur="setTotal(' + index + ')" value=""/></td>' +
            '<td id=""><select name="munit[]" id="' + munit + '" lay-verify="required"></select></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="' + price_id + '" placeholder="请输入价格" onblur="setTotal(' + index + ')"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="' + amount_id + '" value="" placeholder="请输入金额"  onblur="setTotal(' + index + ')"/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect('drugs_id1',selectid);
        createSelect('munit1',munit);
        addDateField(edateid,index);
        form.render();
        //form.render('select');
    })

    function createSelect(sourceid,selectid) {
        var source = document.getElementById(sourceid);
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }
    // 动态添加日期控件的函数
    function addDateField(edateid,index) {
        var container = document.getElementById(edateid);
        var newDateInput = document.createElement('input');
        newDateInput.type = 'text';
        newDateInput.className = 'layui-input';
        newDateInput.placeholder = '有效期';
        newDateInput.name = 'edate[]';
        newDateInput.id = 'edate'+index;
        newDateInput.readOnly = 'readonly';
        newDateInput.removeAttribute("lay-key");
        // 添加日期控件
        container.appendChild(newDateInput);

        // 初始化日期控件
        layui.laydate.render({
            elem: newDateInput, // 指定元素
            format: 'yyyy-MM-dd',
            trigger: 'click'
        });

        form.render();
    }
    return Controller;
});