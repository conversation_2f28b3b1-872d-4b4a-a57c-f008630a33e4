define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundriesbuy/index',
        add_url: 'goods.sundriesbuy/add',
        edit_url: 'goods.sundriesbuy/edit',
        delete_url: 'goods.sundriesbuy/delete',
        export_url: 'goods.sundriesbuy/export',
        modify_url: 'goods.sundriesbuy/modify',
        print_url: 'goods.sundriesbuy/print',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sundriesList.name', minWidth:80,title: '物品名称'},
                    {field: 'cdate', minWidth:80,title: '购买日期',search:'range'},
                    {field: 'num', minWidth:80,title: '数量',search:false},
                    {field: 'sundriesList.munit', minWidth:80,title: '单位',search:false},
                    {field: 'price', minWidth:80,title: '单价',search:false,templet: ea.table.price},
                    {field: 'amount', minWidth:80,title: '总价',search:false,templet: ea.table.price},
                    {field: 'factory.factoryname', title: '厂区',search: true},
                    {field: 'supperList.comname', title: '供应商',search:false,templet:function(d){
                        if(d.supper_id == 0){
                            return ''
                        } else{
                            return d.supperList.comname
                        }
                        }},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '打印入库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            zn.resultPrice()
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            zn.resultPrice()
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };

    return Controller;
});