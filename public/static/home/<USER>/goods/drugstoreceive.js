define(["jquery", "easy-admin" ,"znegdoo"], function ($, ea, zn) {
    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.drugstoreceive/index',
        add_url: 'goods.drugstoreceive/add',
        edit_url: 'goods.drugstoreceive/edit',
        delete_url: 'goods.drugstoreceive/delete',
        export_url: 'goods.drugstoreceive/export',
        print_url: 'goods.drugstoreceive/print',
    };

    var Controller = {

        index: function () {
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '领取',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    },{
                        text: '打印月报表',
                        url: init.print_url,
                        method: 'open',
                        auth: 'edit',
                        class: 'layui-btn layui-btn-warm layui-btn-sm',
                        icon: 'fa fa-print ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'drugsList.drugsname', title: '药品名称'},
                    {field: 'num', title: '领取数量',search: false},
                    {field: 'worker.name', title: '领取人',search: false},
                    {field: 'receive_date', title: '领取日期',search: 'range'},
                    {field: 'batchname', title: '批次',search: false},
                    {field: 'prod_id', minWidth:80,title: '所用鸡舍',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'factory.factoryname', title: '厂区',search: false},
                    {field: 'drugsList.munit', title: '计量单位',search: false},
                    {field: 'remark', title: '备注', templet: ea.table.text,search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            console.log(zn.mytemp());
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);//获取厂区所在鸡舍
                //zn.getWarehouse(data.value,3);//获取厂区所在仓库
            })
            form.on('select(houseid)',function (data){
                setBatch(data.value);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_date));
                data.receive_date=date.getTime()/1000;
                return data;
            });

        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);//获取厂区所在鸡舍
                //zn.getWarehouse(data.value,3);//获取厂区所在仓库
            })
            form.on('select(houseid)',function (data){
                setBatch(data.value);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_date));
                data.receive_date=date.getTime()/1000;
                return data;
            });
        },
        print:function (){
            ea.listen();
        }
    };
    //设置batch_id值
    function setBatch(houseid){
        $.ajax({
            url:'../Common/getBatch',
            data:{houseid:houseid},
            type:'get',
            success:function(res){
                let batch_id=res.data;
                $('#batch_id').val(batch_id);
            },
            error:function () {
                console.log("出错了");
            },//表示如果请求响应出现错误，会执行的回调函数
            dataType:"json" //设置接受到的响应数据的格式
        })
    }
    return Controller;
});