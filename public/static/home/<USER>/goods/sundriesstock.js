define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundriesstock/index',
        add_url: 'goods.sundriesstock/add',
        edit_url: 'goods.sundriesstock/edit',
        delete_url: 'goods.sundriesstock/delete',
        export_url: 'goods.sundriesstock/export',
        modify_url: 'goods.sundriesstock/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar:['refresh'],
                cols: [[
                    {field: 'warehouse',minWidth:80, title: '仓库'},
                    {field: 'name', minWidth:80, title: '物品名称'},
                    {field: 'brand', minWidth:80, title: '品牌',search:false},
                    {field: 'buyNum', minWidth:80, title: '购置总数',search:false},
                    {field: 'receiveNum', minWidth:80, title: '领取总数',search:false},
                    {field: 'stockNum', minWidth:80, title: '库存数量',search:false},
                    {field: 'munit', width:80,title: '单位',search:false},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});