define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.vaccinereceive/index',
        add_url: 'goods.vaccinereceive/add',
        edit_url: 'goods.vaccinereceive/edit',
        delete_url: 'goods.vaccinereceive/delete',
        export_url: 'goods.vaccinereceive/export',
        modify_url: 'goods.vaccinereceive/modify',
        print_url: 'goods.vaccinereceive/print',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '领取',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    },{
                        text: '打印月报表',
                        url: init.print_url,
                        method: 'open',
                        auth: 'edit',
                        class: 'layui-btn layui-btn-warm layui-btn-sm',
                        icon: 'fa fa-print ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'vaccineList.name', title: '名称'},
                    {field: 'pid', title: '生产批号'},
                    {field: 'num', title: '领取数量',search: false},
                    {field: 'worker.name', title: '领取人',search: false},
                    {field: 'receive_date', title: '领取日期',search: 'range'},
                    {field: 'batchname', title: '批次',search: false},
                    {field: 'houseprod.house.housename', title: '所用鸡舍'},
                    {field: 'vaccineList.munit', title: '计量单位',search: false},
                    {field: 'factory.factoryname', title: '厂区',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            form.on('select(houseid)',function (data){
                setBatch(data.value);
            })
            ea.listen(function(data){
                var date = new Date(ea.formatDate(data.receive_date));
                data.receive_date = date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            form.on('select(houseid)',function (data){
                setBatch(data.value);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_date));
                data.receive_date=date.getTime()/1000;

                return data;
            });
        },
        print:function (){
            ea.listen();
        }
    };
    //设置batch_id值
    function setBatch(houseid){
        $.ajax({
            url:'../Common/getBatch',
            data:{houseid:houseid},
            type:'get',
            success:function(res){
                console.log(res);
                let batch_id=res.data;
                $('#batch_id').val(batch_id);
            },
            error:function () {
                console.log("出错了");
            },//表示如果请求响应出现错误，会执行的回调函数
            dataType:"json" //设置接受到的响应数据的格式
        })
    }
    return Controller;
});

