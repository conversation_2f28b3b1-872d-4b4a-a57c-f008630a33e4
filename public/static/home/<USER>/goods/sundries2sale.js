define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.sundries2sale/index',
        add_url: 'goods.sundries2sale/add',
        edit_url: 'goods.sundries2sale/edit',
        delete_url: 'goods.sundries2sale/delete',
        export_url: 'goods.sundries2sale/export',
        modify_url: 'goods.sundries2sale/modify',
        sellprint_url: 'goods.sundries2sale/sellprint',
    };

    var Controller = {

        index: function () {
            var sundriesList = zn.getSundriesList();
            var factoryList = zn.getFactory();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'rele_no', title: '关联单号'},
                    {field: 'cdate', title: '日期'},
                    {field: 'sundries_id', minWidth:80,title: '物料名称',search:"select",selectList:sundriesList,templet: function (d){
                            return d.name;
                        }},
                    {field: 'num', title: '数量',search:false},
                    {field: 'price', title: '单价',search:false},
                    {field: 'amount', title: '总价',search:false},
                    {field: 'factory_id', minWidth:80,title: '厂区',search:"select",selectList:factoryList,templet:function(d){
                        return d.factory.factoryname;
                        }},
                    {field: 'custom_name', title: '销售客户',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印销售单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };
    return Controller;
});