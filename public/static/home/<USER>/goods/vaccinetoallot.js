define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.vaccinetoallot/index',
        add_url: 'goods.vaccinetoallot/add',
        edit_url: 'goods.vaccinetoallot/edit',
        delete_url: 'goods.vaccinetoallot/delete',
        export_url: 'goods.vaccinetoallot/export',
        modify_url: 'goods.vaccinetoallot/modify',
        print_url: 'goods.vaccinetoallot/print',
    };
    var index = Number($('#index').val());
    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '调拨出库',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '单号',search: false},
                    {field: 'action', title: '性质',selectList:{0:"调拨出库",1:"调拨出库"},search: false},
                    {field: 'cdate', title: '日期',search:'range'},
                    {field: 'source_name', title: '调出单位',search: false},
                    {field: 'target_name', title: '调入单位',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '打印出库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };
    var form = layui.form;
    form.on('select(source_id)',function (data){
        $.ajax({
            type:'get',
            url:'../Common/getVaccineData',
            data:{
                id:data.value
            },
            dataType:'json',
            success:function(res){
                console.log(res);
                var select = document.getElementById('vaccine_id1');
                var options = [];
                select.innerHTML='';
                res.forEach(function(item,index){
                    var value = item.vaccine_id;
                    var text = item.name+"（库存："+item.stock_num+item.munit+")";
                    options[index] = document.createElement('option');
                    options[index].value = value;
                    options[index].text = text;
                    select.appendChild(options[index]);
                })
                form.render('select');
            }
        })

    });
    $('#add').on('click', function () {
        index++;
        var selectid = 'vaccine_id' + index;
        var div_id = 'div' + index;
        var num_id = 'num' + index;
        var munit = 'munit' + index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id=""><select name="vaccine_id[]" id="' + selectid + '" lay-verify="required"></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="' + num_id + '" placeholder="请输入数量"  value=""/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect('vaccine_id1',selectid);
        form.render();
    })

    function createSelect(sourceid,selectid) {
        var source = document.getElementById(sourceid);
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }

    return Controller;
});