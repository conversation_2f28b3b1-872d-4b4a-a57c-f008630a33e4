define(["jquery", "easy-admin","znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.rawreceive/index',
        add_url: 'goods.rawreceive/add',
        edit_url: 'goods.rawreceive/edit',
        delete_url: 'goods.rawreceive/delete',
        export_url: 'goods.rawreceive/export',
        modify_url: 'goods.rawreceive/modify',
    };

    var Controller = {

        index: function () {
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'rawList.rawname', title: '原料名称'},
                    {field: 'factory.factoryname', title: '厂区'},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'cdate', title: '领取时间',search:'range'},
                    {field: 'num', title: '数量（公斤）',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
        edit: function () {
            ea.listen(function(data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
    };
    return Controller;
});