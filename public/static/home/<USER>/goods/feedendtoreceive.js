define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'goods.feedendtoreceive/index',
        add_url: 'goods.feedendtoreceive/add',
        edit_url: 'goods.feedendtoreceive/edit',
        delete_url: 'goods.feedendtoreceive/delete',
        export_url: 'goods.feedendtoreceive/export',
        modify_url: 'goods.feedendtoreceive/modify',
        getbill_url: 'goods.feedendtoreceive/getbill',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '领取',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    },{
                        text: '打印月报表',
                        url: init.print_url,
                        method: 'open',
                        auth: 'edit',
                        class: 'layui-btn layui-btn-warm layui-btn-sm',
                        icon: 'fa fa-print ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'feedendList.feedname', title: '饲料名称'},
                    //{field: 'wareHouse.name', title: '领取仓库',search: false},
                    {field: 'num', title: '领取数量（Kg）',search: false},
                    {field: 'receive_time', title: '领取时间',search: 'range'},
                    {field: 'factory.factoryname', minWidth:80,title: '厂区',selectList:factoryList},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'batchname', title: '批次',search: false},
                    {field: 'worker.name', title: '领取人',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool, operat: [[
                            {
                                text: '打印领料单',
                                url: init.getbill_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                //getHouseProd(data.value,form);
                //计算库存
                zn.getHouseProd(data.value);
            })
            form.on('select(feedend_id)',function (data){
                //计算库存
                let factory_id=$('#factory_id').val();
                getStock(data.value,factory_id);
            })
            // form.on('select(warehouse_id)',function(){
            //     //计算库存
            //     let warehouse_id = $('#warehouse_id').val();
            //     let feedend_id = $('#feedend_id').val();
            //     getStock(feedend_id,warehouse_id);
            // })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_time));
                data.receive_time=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            form.on('select(feedend_id)',function (data){
                //计算库存
                let warehouse_id=$('#warehouse_id').val()
                getStock(data.value);
            })
            form.on('select(warehouse_id)',function(){
                //计算库存
                let warehouse_id = $('#warehouse_id').val();
                let feedend_id = $('#feedend_id').val();
                getStock(feedend_id,warehouse_id);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.receive_time));
                data.receive_time=date.getTime()/1000;
                return data;
            });
        },
        print:function (){
            ea.listen();
        }
    };
    function getStock(feedend_id,warehoues_id){
        $.ajax({
            url:'../Common/feedendStock',
            data:{
                feedend_id:feedend_id,
                warehouse_id:warehoues_id
            },
            type:'get',
            dataType: 'json',
            success: function (res) {
                console.log(res);
                let val='库存：'+res;
                $('#stock').html(val);

            }
        })
    }
    return Controller;
});