// define(["jquery", "easy-admin", "echarts", "echarts-theme", "china", "miniAdmin", "miniTab"], function ($, ea, echarts, theme, china, miniAdmin, miniTab) {
//
// 你需要严格从这里注释的代码补全，补全一份与注释相同的代码
//
//     const Controller = {
//         index() {
//             miniAdmin.render({
//                 iniUrl: ea.url('ajax/initAdmin'),
//                 clearUrl: ea.url("ajax/clearCache"),
//                 urlHashLocation: true,
//                 multiModule: true,
//                 maxTabNum: 20
//             });
//
//             $('.login-out').on("click", () => {
//                 ea.request.get({url: 'login/out', prefix: true}, res => {
//                     ea.msg.success(res.msg, () => location.href = ea.url('login/index'));
//                 });
//             });
//         },
//
//         welcome() {
//             miniTab.listen();
//
//             const openLayer = ({title, time, content, width = '75%'}) => {
//                 layer.open({
//                     type: 1,
//                     title: `${title}<span style="float:right;font-size:12px;color:#b1b3b9">${time}</span>`,
//                     area: width,
//                     shade: 0.8,
//                     id: 'layuimini-notice',
//                     btn: ['关闭'],
//                     btnAlign: 'c',
//                     moveType: 1,
//                     content
//                 });
//             };
//
//             const htmlTemplate = (title, author, content, isNotice) => `
// <div style="padding:15px 20px;background:#2f4056;color:#fff">
//     <div style="text-align:center;margin-bottom:15px;font-weight:bold;border-bottom:1px solid #718fb5;padding-bottom:5px">
//         ${isNotice
//                 ? `<h2 style="color:red">${title}</h2><h4>${author}</h4>`
//                 : `<h4 style="color:red">${title}</h4>`}
//     </div>
//     <div style="font-size:${isNotice ? 'inherit' : '12px'}">${content}</div>
// </div>`;
//
//
//             $('body').on('click', '#notice, #msg', function () {
//                 const $t = $(this);
//                 const isNotice = $t.attr('id') === 'notice';
//                 const title = $t.find('.layuimini-notice-title').text();
//                 const time = $t.find('.layuimini-notice-extra').text();
//                 const author = $t.find('#author').text();
//                 let content = $t.find('.layuimini-notice-content').html();
//
//                 if (isNotice) { // HTML 解码
//                     const temp = document.createElement("div");
//                     temp.innerHTML = content;
//                     content = temp.innerText || temp.textContent;
//                 }
//
//                 openLayer({
//                     title: isNotice ? '系统公告' : '系统消息',
//                     time,
//                     width: isNotice ? '75%' : '450px',
//                     content: htmlTemplate(title, author, content, isNotice)
//                 });
//
//                 if (!isNotice) {
//                     $.getJSON("/home/<USER>/reader", {id: $t.find('#id').text()})
//                         .fail(err => console.error('请求失败:', err));
//                 }
//             });
//
//             // 初始化 Tab
//             const activeTabId = $('.layui-tab-title .layui-this').attr('id');
//             indexTab(activeTabId);
//
//             layui.use('element', () => {
//                 layui.element.on('tab', function () {
//                     indexTab(this.id);
//                     layui.element.render();
//                 });
//             });
//         },
//
//         editAdmin: ea.listen,
//         editPassword: ea.listen
//     };
//
//     function indexTab(sn) {
//         $.post("bigview_info_echart", {sn}, data => runCharts(data), 'json')
//             .fail(err => console.error('图表数据请求失败:', err));
//     }
//
//     function runCharts(data) {
//         const echartsRecords = echarts.init(document.getElementById('echarts-wd'), 'walden');
//         echartsRecords.setOption({
//             title: {text: '温度图表'},
//             tooltip: {trigger: 'axis'},
//             legend: {data: ['温度一', '温度二', '温度三']},
//             grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true},
//             toolbox: {feature: {saveAsImage: {}}},
//             xAxis: {type: 'category', boundaryGap: false, data: data.xdata},
//             yAxis: {type: 'value'},
//             series: ['wd1', 'wd2', 'wd3'].map((key, i) => ({
//                 name: `温度${i + 1}`,
//                 type: 'line',
//                 data: data[key]
//             }))
//         });
//         window.addEventListener("resize", () => echartsRecords.resize());
//     }
//
//     return Controller;
// });


define(["jquery", "easy-admin", "echarts", "echarts-theme", "china", "miniAdmin", "miniTab"], function ($, ea, echarts, theme, china, miniAdmin, miniTab) {

    const Controller = {
        index() {
            miniAdmin.render({
                iniUrl: ea.url('ajax/initAdmin'),
                clearUrl: ea.url("ajax/clearCache"),
                urlHashLocation: true,
                multiModule: true,
                maxTabNum: 25
            });
            $('.login-out').on("click", () => {
                ea.request.get({url: 'login/out', prefix: true}, res => {
                    ea.msg.success(res.msg, () => location.href = ea.url('login/index'));
                });
            });
        },

        welcome() {
            miniTab.listen();

            const openLayer = ({title, time, content, width = '75%'}) => {
                layer.open({
                    type: 1,
                    title: `${title}<span style="float:right;font-size:12px;color:#b1b3b9">${time}</span>`,
                    area: width,
                    shade: 0.8,
                    id: 'layuimini-notice',
                    btn: ['关闭'],
                    btnAlign: 'c',
                    moveType: 1,
                    content
                });
            }

            const htmlTemplate = (title, author, content, isNotice) => `
            <div style="padding:15px 20px;background:#2f4056;color:#fff">
                <div style="text-align:center;margin-bottom:17px;font-weight:bold;border-bottom:1px solid #718fb5;padding-bottom:7px">
                    ${isNotice
                            ? `<h2 style="color:red">${title}</h2><h4>${author}</h4>`
                            : `<h4 style="color:red">${title}</h4>`}
                </div>
                <div style="font-size:${isNotice ? 'inherit' : '12px'}">${content}</div>
            </div>`;

            $('body').on('click', '#notice, #msg', function () {
                const $t = $(this);
                const isNotice = $t.attr('id') === 'notice';
                const title = $t.find('.layuimini-notice-title').text();
                const time = $t.find('.layuimini-notice-extra').text();
                const author = $t.find('#author').text();
                const content = $t.find('.layuimini-notice-content').html();

                openLayer({
                    title: isNotice ? '系统公告' : '系统消息',
                    time,
                    width: isNotice ? '75%' : '450px',
                    content: htmlTemplate(title, author, content, isNotice)
                });

                if (isNotice) {
                    const temp = document.createElement("div");
                    temp.innerHTML = content;
                    console.log(temp.innerText || temp.textContent);
                }

                openLayer({
                    title: isNotice ? '系统公告' : '系统消息',
                    time,
                    width: isNotice ? '75%' : '450px',
                    content: htmlTemplate(title, author, content, isNotice)
                });

                if (!isNotice) {
                    $.getJSON("/home/<USER>/reader", {id: $t.find('#id').text()})
                        .fail(err => console.error('请求失败:', err));
                }

            });

            // 初始化 Tab
            const activeTabId = $('.layui-tab-title .layui-this').attr('id');
            indexTab(activeTabId);

            layui.use('element', () => {
                layui.element.on('tab', function () {
                    indexTab(this.id);
                    layui.element.render();
                });
            });

        },

        editAdmin: ea.listen,
        editPassword: ea.listen
    }

    function indexTab(sn) {
        $.post("bigview_info_echart", {sn}, data => runCharts(data), 'json')
            .fail(err => console.error('图表数据请求失败:', err));
    }

    function runCharts(data) {
        const echartsRecords = echarts.init(document.getElementById('echarts-wd'), 'walden');
        echartsRecords.setOption({
            title: { text: '温度图表' },
            tooltip: { trigger: 'axis' },
            legend: { data: ['温度一', '温度二', '温度三'] },
            grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
            toolbox: { feature: { saveAsImage: {} } },
            xAxis: { type: 'category', boundaryGap: false, data: data.xdata },
            yAxis: { type: 'value' },
            series: ['wd1', 'wd2', 'wd3'].map((key, i) => ({
                name: `温度${i + 1}`,
                type: 'line',
                data: data[key]
            }))
        });
        window.addEventListener("resize", () => echartsRecords.resize());
    }

    return Controller;
})