define(["jquery", "easy-admin", "echarts", "echarts-theme", "miniAdmin", "miniTab"], function ($, ea, echarts, undefined, miniAdmin, miniTab) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'stats.envchart/index',
    };


    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            $.ajax({
                url: 'index',
                data: {},
                type: 'post',
                success: function (res) {
                    console.log(res);
                    getEchartsVal(res);
                }
            })
            ea.listen(function (data) {
                return data;
            }, function (d) {
                console.log('success');
            }, function (e) {
                //console.log('error');
                console.log(e);
                getEchartsVal(e);
            }, function (ex) {
                console.log('ex');
            });

        },


    };

    function getEchartsVal(e) {
        var code, cdate, data = [], xAxis = [], yAxis = [], legend = [], series = [];
        code = e.code;
        if (code == 0) {
            layer.alert(e.msg);
        } else {
            data = e.data;
            xAxis = data['xaxis'];
            yAxis = data['yaxis'];
            series = data['series'];
            legend = data['legend'];
            cdate = data['cdate'];
            $('#cdate').val(cdate);
            var itemSeries = [];
            const itemName = {
                wd1:'温度一',
                wd2:'温度二',
                wd3:'温度三',
                wd4:'温度四',
                wd5:'温度五',
                wd6:'温度六',
                wd7:'温度七',
                wd8:'温度八',
                pjwd:'平均温度',
                sd:'湿度',
                co2:'二氧化碳',
                yl:'负压',
                swwd:'室外温度',
                swsd:'室外湿度',
            }
            for(let i in series){
                const itemId = 'echarts-'+i;
                const seriesArr = series[i];
                const legendArr = legend[i];
                runEcharts(itemName[i], legendArr, xAxis, yAxis, seriesArr, itemId);
            }
        }

    }

    function runEcharts(title, legend, xAxis, yAxis, series, item) {
        /**
         * 图表
         */
        var echartsRecords = echarts.init(document.getElementById(item), 'walden');
        var optionRecords = {
            title: {
                show: series.length,
                text: title
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                x:'center',
                y:'top',
                left:'20%',
                data: legend,

            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '6%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xAxis
            },
            yAxis: yAxis,
            series: series,
        };
        echartsRecords.setOption(optionRecords, true);
        window.addEventListener("resize", function () {
            echartsRecords.resize();
        });
    }

    return Controller;
});

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return (false);
}

