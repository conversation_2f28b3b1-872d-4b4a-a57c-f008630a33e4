define(["jquery", "easy-admin", "echarts", "echarts-theme", "miniAdmin", "miniTab"], function ($, ea, echarts, undefined, miniAdmin, miniTab) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'stats.supperchart/index',
    };


    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            getCharts();

            ea.listen(function (data) {
                return data;
            }, function (d) {
                console.log('success');
            }, function (e) {
                //提交成功回调
                console.log(e);
                runEcharts(e);
            }, function (ex) {
                console.log('ex');
            });

        },


    };
    //显示图表
    function runEcharts(e){
        var code, data = [], xAxis = [], series = [], legend = [],yAxis=[];
        code = e.code;
        if (code == 0) {
            layer.alert(e.msg);
        } else {
            data = e.data;
            legend = data['legend'];
            xAxis = data['xaxis'];
            series = data['series'];
            yAxis = data['yaxis'];
        }
        const itemName = {
            buyamount:'采购额',
            buynum:'采购量',
        }
        for(let i in series) {
            const itemId = 'echarts-' + i;
            const seriesArr = series[i];
            const legendArr = legend[i];
            console.log(seriesArr);
            /**
             * 图表
             */
            var echartsRecords = echarts.init(document.getElementById(itemId), 'walden');
            var optionRecords = {
                title: {
                    text: itemName[i]
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: legendArr
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxis
                },
                yAxis: yAxis,
                series: seriesArr
            };
            echartsRecords.setOption(optionRecords, true);
            window.addEventListener("resize", function () {
                echartsRecords.resize();
            });
        }
    }

    //请求图表数据
    function getCharts(){
        $.ajax({
            type:'post',
            url:'index',
            data:{
            },
            dataType:'json',
            success:function (res){
                console.log(res);
                runEcharts(res);
            }
        })
    }
    return Controller;
});