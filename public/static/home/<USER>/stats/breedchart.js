define(["jquery", "easy-admin", "echarts", "echarts-theme", "miniAdmin", "miniTab"], function ($, ea, echarts, undefined, miniAdmin, miniTab) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'stats.breedchart/index',
    };
    var chartsData=[];

    var Controller = {

        index: function () {
            miniTab.listen();
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            form.on('radio(dateitem)', function (data) {
                //console.log(data.elem); //得到radio原始DOM对象
                console.log(data.value); //被点击的radio的value值
                if (data.value == 1) {
                    $('#daterange').show();
                } else {
                    $('#daterange').hide();
                }
                if(data.value == 0){
                    $('#daysrange').show();
                }else{
                    $('#daysrange').hide();
                }
            });

            form.on('select(factory_id)', function (data) {
                $.ajax({
                    url: '../Common/getHouseProd',
                    data: {
                        factory_id: data.value
                    },
                    type: 'get',
                    dataType: 'json',
                    success: function (res) {
                        console.log(res);
                        $('#house').empty();
                        res.forEach(function (e) {
                            var obj = '<div class="layui-input-inline">' +
                                '<input type="checkbox" name="housename[' + e.houseid + ']" lay-skin="primary" title="' + e.housename + '" value="' + e.houseid + '">' +
                                '<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>' + e.housename + '</span>' +
                                '<i class="layui-icon layui-icon-ok"></i></div></div>';
                            $('#house').append(obj);
                        })
                        form.render();
                    }
                })
            })
            $('body').on('click', '.lbh-btn', function () {
                var id = $(this).attr('id');//获取当前ID，判断项目
                let legend = chartsData['legend'];
                let xAxis = chartsData['xaxis'];
                let xAxis_qua = chartsData['xaxis_qua'];
                let series = chartsData['series'];
                let yAxis = chartsData['yaxis'];
                const seriesArr = series[id];
                const legendArr = legend[id];
                if (id === 'qua') xAxis = xAxis_qua;

                const itemName = {
                    feednum: '耗料量(克)',
                    eggnum: '产蛋数(枚)',
                    egglu: '产蛋率(%)',
                    qua: '体重胫长达标率(%)',
                    feedeggbi: '料蛋比',
                    badegglu: '破蛋率(%)',
                    deadnum: '死淘数(只)',
                    waternum: '用水量(克)',
                    powernum: '用电量(kw/h)',
                }
                /**
                 * 图表
                 */
                var echartsRecords = echarts.init(document.getElementById('chartsFull'), 'walden');
                var optionRecords = {
                    title: {
                        text: ''  //itemName[i]
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: legendArr
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: xAxis,
                        axisLabel: {
                            interval: 'auto', // 自动计算间隔
                            rotate: 45        // 旋转标签以避免重叠
                        }
                    },
                    yAxis: yAxis,
                    series: seriesArr
                };
                echartsRecords.setOption(optionRecords, true);
                window.addEventListener("resize", function () {
                    echartsRecords.resize();
                });

                layer.open({
                    type: 1,
                    title: itemName[id],
                    content: $('#chartsFull'), // 设置弹出层内容
                    area: ['800px', '880px'], // 设置弹出层尺寸
                    shadeClose: true, // 点击遮罩层关闭弹出层
                    shade: 0.3,
                    id: 'layuimini-notice',
                    moveType: 1,
                    end: function() { // close 关闭回调
                        $('#chartsFull').hide();
                    }
                });


            });
            ea.listen(function (data) {
                return data;
            }, function (d) {
                console.log('success');
            }, function (e) {
                console.log(e);
                runEcharts(e);
            }, function (ex) {
                console.log(ex);
            });

        },


    };


    //显示图表
    function runEcharts(e) {
        $('#charts').show();
        var code, data = [], xAxis = [], series = [], legend = [], yAxis = [], xAxis_qua = [];
        code = e.code;
        if (code == 0) {
            layer.alert(e.msg);
        } else {
            data = e.data;
            legend = data['legend'];
            xAxis = data['xaxis'];
            xAxis_qua = data['xaxis_qua'];
            series = data['series'];
            yAxis = data['yaxis'];

            chartsData = data;
        }
        const itemName = {
            feednum: '耗料量(Kg)',
            eggnum: '产蛋数(枚)',
            egglu: '产蛋率(%)',
            qua: '体重胫长达标率(%)',
            feedeggbi: '料蛋比',
            badegglu: '破蛋率(%)',
            deadnum: '死淘数(只)',
            waternum: '用水量(吨)',
            powernum: '用电量(kw/h)',
        }
        for (let i in series) {
            const itemId = 'echarts-' + i;
            const seriesArr = series[i];
            const legendArr = legend[i];
            console.log(legendArr+itemId);
            if (i === 'qua') xAxis = xAxis_qua;
            /**
             * 图表
             */
            var echartsRecords = echarts.init(document.getElementById(itemId), 'walden');
            var optionRecords = {
                title: {
                    text: ''  //itemName[i]
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: legendArr
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxis,
                    axisLabel: {
                        interval: 'auto', // 自动计算间隔
                        rotate: 45        // 旋转标签以避免重叠
                    }
                },
                yAxis: yAxis,
                series: seriesArr
            };
            echartsRecords.setOption(optionRecords, true);
            window.addEventListener("resize", function () {
                echartsRecords.resize();
            });
        }
    }

    //请求图表数据
    function getCharts() {
        $.ajax({
            type: 'post',
            url: 'index',
            data: {},
            dataType: 'json',
            success: function (res) {
                console.log(res);
                runEcharts(res);
            }
        })
    }

    return Controller;
});