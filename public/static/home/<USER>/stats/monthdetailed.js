define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {
    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'stats.monthdetailed/index',
    };
    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , table = layui.table
                , form = layui.form;
            form.on('select(factory_id)', function (data) {
                zn.getHouseProd(data.value, form);
            })
            $('body').on('click', '.layui-btn', function () {
                var factory_id = $('#factory_id').val();
                var prod_id = $('#prod_id').val();
                var month = $('#month').val();
                if(factory_id=='' || prod_id=='' || month==''){
                    layer.msg('厂区、鸡舍不能为空！',{icon:2});
                    return false;
                }
                table.render({
                    init:init,
                    elem: '#currentTable',
                    url: 'index', // 后端接口地址
                    where: {factory_id: factory_id, prod_id: prod_id, month: month},
                    cols: [[
                        {field: 'prod_date', title: '日期'},
                        {field: 'days', title: '日龄'},
                        {field: 'cunlan', title: '存栏'},
                        {field: 'eggnum', title: '产蛋数(枚)', search: false},
                        {field: 'goodegg', title: '合格蛋(枚)', search: false},
                        {field: 'eggweight', title: '产蛋量(KG)', search: false},
                        {field: 'eggweightavg', title: '平均蛋重(g)', search: false},
                        {field: 'feednum', title: '用料量(Kg)', search: false},
                        {field: 'waternum', title: '用水量(Kg)', search: false},
                        {field: 'powernum', title: '用电量(kwh)', search: false},
                        {field: 'egglu', title: '产蛋率(%)', search: false},
                        {field: 'feedeggbi', title: '料蛋比', search: false},
                        {field: 'deadnum', title: '死淘数(只)', search: false},
                        {field: 'diseaseInfo', title: '疾病免疫', search: false},
                        {field: 'eggcost', title: '造蛋成本', search: false},

                    ]],
                    parseData: function (res) { // 解析返回的 JSON 数据
                        const otherData = res.other_data;
                        // 更新其他数据
                        $('#egglu90').text(otherData['egglu90']);
                        $('#egglu95').text(otherData['egglu95']);
                        $('#egglu96').text(otherData['egglu96']);
                        $('#egglu97').text(otherData['egglu97']);
                        $('#year').text(otherData['year']);
                        $('#name_type').text(otherData['name_type']);
                        // 返回表格数据（符合 Layui 表格格式）
                        // return {
                        //     code: res.code,
                        //     msg: res.msg,
                        //     count: res.count,
                        //     data: res.data
                        // };
                    }

                });
            });
            ea.listen();
        },


    };
    return Controller;
});