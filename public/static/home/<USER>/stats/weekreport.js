define(["jquery", "easy-admin", "echarts", "echarts-theme", "miniAdmin", "miniTab"], function ($, ea, echarts, undefined, miniAdmin, miniTab) {

    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            var laydate = layui.laydate;
            form.on('submit(week)', function (data) {
                console.log(data.elem) //被执行事件的元素DOM对象，一般为button对象
                console.log(data.form) //被执行提交的form对象，一般在存在form标签时才会返回
                console.log(data.field) //当前容器的全部表单字段，名值对形式：{name: value}
                //return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
            });
            var year = $("#year").val();
            console.log(year);
            var obj = $("#week");
            getWeekDate(year, obj, form);


            //执行一个laydate实例
            laydate.render({
                elem: '#year',
                type: 'year',
                done: function (value, date) {
                    console.log(date);
                    getWeekDate(date.year, obj, form);
                }
            });

            ea.listen();
        },


    };

    function getWeekDate(year, obj, form) {
        var maxWeek = getNumOfWeeks(year);
        var weekArr = [], temparr = [];
        for (let w = 0; w < maxWeek; w++) {
            temparr = week_date(year, w + 1);
            weekArr[w] = "第" + (w + 1) + "周" + "(" + temparr[0] + ' - ' + temparr[1] + ")";
        }
        obj.html("");
        $.each(weekArr, function (key, val) {
            var option1 = $("<option>").val(key + 1).text(val);
            //通过LayUI.jQuery添加列表项
            obj.append(option1);
            form.render('select');
        })
    }

    return Controller;
});