define(["jquery", "easy-admin"], function ($, ea) {

    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;

            $('#saveMonth').on('click', function() {
                // 点击按钮弹出确认框
                layer.confirm('请仔细查看数据是否正确？', {icon: 3, title:'提示'}, function(index){
                    // 确认执行的回调
                    // 例如：发起Ajax请求，执行操作
                    // 操作完成后，可以关闭弹层
                    saveMonth();
                    layer.close(index);
                    // setTimeout(function(){
                    //
                    // },2000);

                    // 或者如果需要关闭当前页面
                    // window.parent.location.reload(); // 如果是iframe页面
                    // window.location.reload(); // 直接当前页面刷新
                });

            });
            $('#factoryTotal').on('click',function (){
                var cdate = $('#month').val();
                if(cdate===''){
                    layer.alert('请选择月份！');
                    return false;
                }else{
                    layer.open({
                        type: 2,
                        title: '全部厂区数据('+cdate+')',
                        content: 'factorytotal?cdate=' + encodeURIComponent(cdate),
                        area: ['100%', '100%'], // 弹窗的尺寸
                        success: function(layero, index){
                            // 成功打开弹窗后的回调函数
                        }
                    });
                }
            })
            ea.listen();
        },


    };
    function saveMonth(){
        console.log(dataFromPHP); // 输出: value
        var data = JSON.stringify(dataFromPHP);
        $.ajax({
            url:'../Common/eggMonthSave',
            type:'post',
            data:{data:data},
            dataType:'json',
            success:function(res){
                console.log(res);
                if(res){
                    layer.alert('已提交，不可修改！');
                    $('#saveMonth').prop('disabled', true);
                    $('#saveMonth').addClass("layui-btn-disabled");
                }else{
                    layer.alert('提交失败！')
                }
            }
        })
    }
    return Controller;
});