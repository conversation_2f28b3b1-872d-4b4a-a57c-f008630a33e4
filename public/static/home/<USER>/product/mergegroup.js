define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.mergegroup/index',
        add_url: 'product.mergegroup/add',
        edit_url: 'product.mergegroup/edit',
        delete_url: 'product.mergegroup/delete',
        export_url: 'product.mergegroup/export',
        modify_url: 'product.mergegroup/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '合并操作',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'export'],
                cols: [[
                    {type: "checkbox"},
                    {field: 'housename', minWidth: 80, title: '合并鸡舍',search: false},
                    {field: 'targetHouse', minWidth: 80, title: '并入鸡舍'},
                    {field: 'batchname', minWidth: 80, title: '新批次名',search:false},
                    {field: 'num', minWidth: 80,title: '合并数量',search: false},
                    {field: 'typename', minWidth: 80,title: '品种',search: false},
                    {field: 'cdate', minWidth: 80, title: '日期',search: 'range'},

                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat:[[
                            {
                                text: '撤消',
                                url: init.delete_url,
                                method: 'request',
                                auth: 'delete',
                                class: 'layui-btn layui-btn-xs layui-btn-danger',
                                extend: 'data-full="true"',
                            }
                        ]]
                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('checkbox(isbatch)', function(data){
                console.log(this.checked); // 打印checkbox的状态：true或false
                if(this.checked){
                    $('#newBatch').show();
                }else{
                    $('#newBatch').hide();
                }
            });
            //日期转为时间戳
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;

            });
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});