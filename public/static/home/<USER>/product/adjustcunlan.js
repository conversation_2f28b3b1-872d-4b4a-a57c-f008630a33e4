define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.adjustcunlan/index',
        add_url: 'product.adjustcunlan/add',
        edit_url: 'product.adjustcunlan/edit',
        delete_url: 'product.adjustcunlan/delete',
        export_url: 'product.adjustcunlan/export',
        modify_url: 'product.adjustcunlan/modify',
    };

    var Controller = {

        index: function () {
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'num', title: '调整数量',search:false},
                    {field: 'cdate', title: '调整日期',search: 'range'},
                    {field: 'notes', title: '说明',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            })
        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            })
        },
    };
    return Controller;
});