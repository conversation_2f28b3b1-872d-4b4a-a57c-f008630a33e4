define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.deadnote/index',
        add_url: 'product.deadnote/add',
        edit_url: 'product.deadnote/edit',
        delete_url: 'product.deadnote/delete',
        export_url: 'product.deadnote/export',
        modify_url: 'product.deadnote/modify',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,templet: function (d){
                            return d.factory;
                        }},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'cdate', title: '死淘日期',search: 'range'},
                    {field: 'cunlan', title: '存栏',search: false},
                    {field: 'days', title: '日龄',search: false},
                    {field: 'area', title: '死淘区域',search: false},
                    {field: 'num', title: '死淘数量',search: false},
                    {field: 'weight', title: '称重重量(g)',search: false},
                    {field: 'deadCauseName', title: '死淘原因',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });
            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            form.on('select(cause)',function(data){
                var causeid=data.value;
                if(causeid==10){
                    $("#other").show();
                }else{
                    $("#other").hide();
                }
            });
            var laydate=layui.laydate;
            laydate.render({
                elem:'#cdate',//制定元素
                type:'date',
                //range:true,//开启左右面板
                min:'2017-09-1',//
                max:Date.now(),//规定时间期限
                done:function(value,date){//value, date, endDate点击日期、清空、现在、确定均会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
                    console.log(value);
                    console.log(date);
                    var prod_id=$('#prod_id option:selected').val();
                    $.ajax({
                        url:'../Common/getDays',
                        data:{
                            prod_id:prod_id,
                            cdate:value
                        },
                        type:'get',
                        dataType:'json',
                        success:function(res){
                            console.log(res);
                            $('#days').val(res);
                        }
                    })
                }
            });

            ea.listen(function (data){
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            form.on('select(cause)',function(data){
                var causeid=data.value;
                if(causeid==10){
                    $("#other").show();
                }else{
                    $("#other").hide();
                }
            });
            var laydate=layui.laydate;
            laydate.render({
                elem:'#cdate',//制定元素
                type:'date',
                //range:true,//开启左右面板
                min:'2017-09-1',//
                max:Date.now(),//规定时间期限
                done:function(value,date){//value, date, endDate点击日期、清空、现在、确定均会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
                    console.log(value);
                    console.log(date);
                    var prod_id=$('#prod_id option:selected').val();
                    $.ajax({
                        url:'../Common/getDays',
                        data:{
                            prod_id:prod_id,
                            cdate:value
                        },
                        type:'get',
                        dataType:'json',
                        success:function(res){
                            console.log(res);
                            $('#days').val(res);
                        }
                    })
                }
            });
            ea.listen(function (data){
                var cdate = new Date(ea.formatDate(data.cdate));
                data.cdate = cdate.getTime() / 1000;
                return data;
            });
        },
    };

    return Controller;
});