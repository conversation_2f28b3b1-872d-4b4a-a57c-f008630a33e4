define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.chickbatch/index',
        add_url: 'product.chickbatch/add',
        edit_url: 'product.chickbatch/edit',
        delete_url: 'product.chickbatch/delete',
        export_url: 'product.chickbatch/export',
        modify_url: 'product.chickbatch/modify',
    };


    var Controller = {

        index: function () {
            var hensList = zn.getHensType();
            var factoryList = zn.getFactory();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: "checkbox"},
                    {field: 'factory_id', minWidth: 80, title: '厂区',selectList:factoryList,searchOp:"=",templet: function (d){
                        if(d.factory_id!=null) return d.factory.factoryname;
                        }},
                    {field: 'batchname', minWidth: 80, title: '批次名称'},
                    {field: 'cdate', minWidth: 80, title: '购买日期',search:'range'},
                    {field: 'days', minWidth: 80, title: '日龄',search: false},
                    // {field: 'type', minWidth: 80, title: '类别',search: false},
                    {field: 'type_id', minWidth: 100, title: '品种',selectList:hensList,templet:function(d){
                            if(d.type_id!=null) return d.henstype.typename;
                        }},
                   {field: 'price', minWidth: 100, title: '单价', templet: ea.table.price,search: false},
                    {field: 'sellnum', minWidth: 80, title: '总数量',search: false},
                    {field: 'amount', minWidth: 100, title: '总价', templet: ea.table.price,search: false},
                    {field: 'valnum', minWidth: 80, title: '未分配',search: false},
                    {field: 'supperList.comname', title: '供应商',search: false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool
                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.brith_time));
                date.setHours(0);
                data.brith_time=date.getTime()/1000;
                var cdate=new Date(ea.formatDate(data.cdate));
                cdate.setHours(0);
                data.cdate=cdate.getTime()/1000;
                data.amount = data.price*data.sellnum;
                return data;
            });
        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.brith_time));
                date.setHours(0);
                data.brith_time=date.getTime()/1000;
                var cdate=new Date(ea.formatDate(data.cdate));
                cdate.setHours(0);
                data.cdate=cdate.getTime()/1000;
                data.amount = data.price*data.sellnum;
                return data;
            });
        },
        stock: function () {
            ea.listen();
        },
    };
    return Controller;
});

