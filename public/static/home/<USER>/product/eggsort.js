define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.eggsort/index',
        add_url: 'product.eggsort/add',
        edit_url: 'product.eggsort/edit',
        delete_url: 'product.eggsort/delete',
        export_url: 'product.eggsort/export',
        modify_url: 'product.eggsort/modify',
        print_url: 'product.eggsort/print',
        search_url: 'product.eggsort/search',
    };
    var index = Number($('#index').val());
    var munit = $('#munit').val();
    var Controller = {

        index: function () {
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                skin: 'row|line',
                size: 'sm',
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '分拣单号', minWidth: 150},
                    {field: 'cdate', title: '日期', minWidth: 80, search: 'range'},
                    {field: 'sort_num_total', title: '分拣数量(枚)', search: false},
                    {field: 'sort_weight_total', title: '分拣重量(' + munit + ')', search: false},
                    {field: 'in_num_total', title: '入库数量(枚)', search: false},
                    {field: 'in_weight_total', title: '入库重量(' + munit + ')', search: false},
                    {
                        width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '打印分拣单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;


            form.on('select(factory_id)', function (data) {
                $.ajax({
                    type: 'get',
                    url: '../Common/getHouseProd',
                    data: {
                        factory_id: data.value
                    },
                    dataType: 'json',
                    success: function (res) {
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML = '';
                        res.forEach(function (item, index) {
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            var form = layui.form;
            form.on('select(factory_id)', function (data) {
                $.ajax({
                    type: 'get',
                    url: '../Common/getHouseProd',
                    data: {
                        factory_id: data.value
                    },
                    dataType: 'json',
                    success: function (res) {
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML = '';
                        res.forEach(function (item, index) {
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        search:function(){
            var cols=[[
                {field: 'housename', title: '鸡舍', minWidth: 150},
                {field: 'cdate', title: '入库日期', minWidth: 80},
                {field: 'sortclass', title: '分拣类型', minWidth: 80},
                {field: 'sort_num', title: '分拣数量(枚)', search: false},
                {field: 'sort_weight', title: '分拣重量(' + munit + ')'},
                {field: 'inclass', title: '入库类型', minWidth: 80},
                {field: 'in_num', title: '入库数量(枚)', search: false},
                {field: 'in_weight', title: '入库重量(' + munit + ')'},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },
        print:function (){
            ea.listen();
        },
    };
    var form = layui.form;
    $('#add').on('click', function () {
        index++;
        if ($('#factory_id').val() == '') {
            layer.alert('需要先选择厂区！');
            return false;
        }
        var house_id = 'houseid' + index;
        var sort_class_id = 'sort_class' + index;
        var div_id = 'div' + index;
        var sort_num_id = 'sort_num' + index;
        var sort_weight_id = 'sort_weight' + index;
        var in_class_id = 'in_class' + index;
        var in_num_id = 'in_num' + index;
        var in_weight_id = 'in_weight' + index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id="house"><select name="prod_id[]" lay-verify="required" id="' + house_id + '"></select></td>' +
            '<td id="type"><select name="sort_class_id[]" lay-verify="required" id="' + sort_class_id + '"></select></td>' +
            '<td><input type="text" class="layui-input" name="m_sort_num[]" id="' + sort_num_id + '" placeholder="枚数"  value="" onblur="setTotal()"/></td>' +
            '<td><input type="text" class="layui-input" name="m_sort_weight[]" id="' + sort_weight_id + '" placeholder="' + munit + '"  value=""  onblur="setTotal()"/></td>' +
            '<td id="type"><select name="in_class_id[]" lay-verify="required" id="' + in_class_id + '"></select></td>' +
            '<td><input type="text" class="layui-input" name="m_in_num[]" id="' + in_num_id + '" placeholder="枚数"  value=""  onblur="setTotal()"/></td>' +
            '<td><input type="text" class="layui-input" name="m_in_weight[]" id="' + in_weight_id + '" placeholder="' + munit + '"  value=""  onblur="setTotal()"/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect('houseid1', house_id);
        createSelect('sort_class1', sort_class_id);
        createSelect('in_class1', in_class_id);
        form.render('select');
    })

    function createSelect(sourceid, selectid) {
        var source = document.getElementById(sourceid);
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }

    function showNote() {
        var form = layui.form;
        form.on('select(batch_id)', function (data) {
            $.ajax({
                type: 'post',
                url: 'getEggNote',
                data: {
                    batch_id: data.value
                },
                dataType: 'json',
                success: function (res) {
                    console.log(res);
                    let note = res.data;
                    $('#note').html(note);
                }
            })
        })
    }

    function getHouseProd(val, form) {
        $.ajax({
            url: '../Common/getHouseProd',
            data: {
                factory_id: val
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                console.log(res);
                $('#prod_id').empty();
                $('#prod_id').append('<option>请选择……</option>');
                res.forEach(function (e) {
                    $('#prod_id').append('<option value="' + e.id + '">' + e.housename + '</option>');
                })
                form.render();
            }
        })
    }

    return Controller;
});