define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.adjust/index',
        add_url: 'product.adjust/add',
        edit_url: 'product.adjust/edit',
        delete_url: 'product.adjust/delete',
        export_url: 'product.adjust/export',
        modify_url: 'product.adjust/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '鸡舍调整',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }],
                    'export'],
                cols: [[
                    {type: "checkbox"},
                    //{field: 'id', width: 80, title: 'ID',search:false},
                    {field: 'batchname', minWidth: 80, title: '批次',search:false},
                    {field: 'sourcehousename', minWidth: 80, title: '原鸡舍'},
                    {field: 'targethousename', minWidth: 80, title: '目的鸡舍',search:false},
                    {field: 'cdate', minWidth: 80, title: '调整日期',search:'range'},
                    {field: 'adjust_num', minWidth: 80, title: '调整数量',search:false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat:[[
                            {
                                text: '撤消调整',
                                url: init.delete_url,
                                method: 'request',
                                auth: 'delete',
                                class: 'layui-btn layui-btn-xs layui-btn-danger',
                                extend: 'data-full="true"',
                            }
                        ]]
                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('submit',function(data){
                console.log(data.field);
                layer.alert('拦截提交');
                return false;
                // 阻止默认的提交行为
                //data.preventDefault();
            })
            form.on('select(source)',function(data){
                $('#sourceCunlan').val(data.elem[data.elem.selectedIndex].dataset.val);
                console.log(data.elem[data.elem.selectedIndex].dataset.batch);
                //$('#sourcenum').val(data.elem[data.elem.selectedIndex].dataset.val);
            });

            form.on('select(target)',function(data){
                //$('#targetnum').val(data.elem[data.elem.selectedIndex].dataset.val);
            });
            form.on('checkbox(adjust_true)', function(data){
                console.log(this.checked); // 打印checkbox的状态：true或false
                if(this.checked){
                    $('#newBatch').show();
                }else{
                    $('#newBatch').hide();
                }
            });
            $('#adjust_num').blur(function(data){
                var _this=$(this);
                var v1=parseInt(_this.val());
                var v2=parseInt($('#sourceCunlan').val());
                if(v1>v2){
                    layer.alert('超出调整范围！');
                    _this.val('');
                }
            });
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
         },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});