define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.eggsreturn/index',
        add_url: 'product.eggsreturn/add',
        edit_url: 'product.eggsreturn/edit',
        delete_url: 'product.eggsreturn/delete',
        export_url: 'product.eggsreturn/export',
        modify_url: 'product.eggsreturn/modify',
        print_url:'product.eggsreturn/print',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'cdate', title: '退货时间'},
                    {field: 'name', title: '操作人',search:false},
                    {field: 'status',title: '状态',selectList: {0: '审核中', 1: '完成退货',2:'审核不通过'}},
                    {field: 'note', title: '退货原因',search:false},
                    {field: 'explain', title: '审核意见',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印退货单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(sale_no)',function(data){
                console.log(data);
                selectNo(data.value);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;

            });
        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;

            });
        },
    };
    function selectNo(id){
        var form=layui.form;
        $.ajax({
            type:'get',
            url:'../Common/getEggSaleData',
            data:{
                id:id
            },
            dataType:'json',
            success:function(res){
                console.log(res);
                let list = res;
                let tbody = '';
                let tbody2 = '';
                let abc = '';
                list.forEach(function(item,index){
                    console.log(item);
                    let num_return_id = 'num_return'+item.id;
                    let weight_return_id = 'weight_return'+item.id;
                    let amount_return_id = 'amount_return'+item.id;
                    let price_return_id = 'price_return'+item.id;
                    let return_num=item.return_num==null?'':item.return_num;
                    let return_weight=item.return_weight==null?'':item.return_weight;
                    let return_price=item.return_price==null?'':item.return_price;
                    let return_amount=item.return_amount==null?'':item.return_amount;
                    tbody += '<tr>' +
                        '<td id="house">'+item.housename+'<input type="hidden" name="sellid[]" value="'+item.id+'"> </td>' +
                        '<td id="type">'+item.typename+'</td>' +
                        '<td>'+item.num+'</td>' +
                        '<td>'+item.weight+'</td>' +
                        '<td>'+item.price+'</td>' +
                        '<td>'+item.amount+'</td>' +
                        '<td><input type="text" class="layui-input" name="return_num[]" id="'+num_return_id+'" value="'+return_num+'" placeholder="枚" onblur="numCheck(this,'+item.num+','+item.id+')"  /></td>' +
                        '<td><input type="text" class="layui-input" name="return_weight[]" id="'+weight_return_id+'" value="'+return_weight+'" placeholder="斤"  onblur="numCheck(this,'+item.weight+','+item.id+')" /></td>' +
                        '<td><input type="text" class="layui-input" name="return_price[]" id="'+price_return_id+'" value="'+return_price+'" placeholder="￥"  onblur="getAmount('+item.id+')" /></td>' +
                        '<td><input type="text" class="layui-input" name="return_amount[]" id="'+amount_return_id+'" value="'+return_amount+'" placeholder="￥"  onblur="amountCheck(this,'+item.amount+','+item.id+')" /></td>' +
                        '</tr>';

                })
                document.getElementById("list").innerHTML = tbody;
            }
        })
        form.render('select');

    }

    return Controller;
});