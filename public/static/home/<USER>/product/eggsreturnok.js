define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.eggsreturnok/index',
        add_url: 'product.eggsreturnok/add',
        edit_url: 'product.eggsreturnok/edit',
        delete_url: 'product.eggsreturnok/delete',
        export_url: 'product.eggsreturnok/export',
        modify_url: 'product.eggsreturnok/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'cdate', title: '退货时间'},
                    {field: 'name', title: '操作人',search:false},
                    {field: 'status',title: '状态',selectList: {0: '审核中', 1: '完成退货',2:'审核不通过'}},
                    {field: 'note', title: '退货原因',search:false},
                    {field: 'explain', title: '审核意见',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '审核',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: '',
                            }
                        ]]
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };

    return Controller;
});