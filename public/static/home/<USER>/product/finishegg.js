define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.finishegg/index',
        add_url: 'product.finishegg/add',
        edit_url: 'product.finishegg/edit',
        delete_url: 'product.finishegg/delete',
        export_url: 'product.finishegg/export',
        modify_url: 'product.finishegg/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '出栏',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }]],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'batchname', title: '批次'},
                    {field: 'housename', title: '鸡舍'},
                    {field: 'cdate', title: '出栏日期',search:'range'},
                    {field: 'num', title: '出栏数量',search:false},
                    {field: 'days', title: '日龄',search:false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat:['edit',[
                            {
                                text: '恢复',
                                url: init.delete_url,
                                method: 'request',
                                auth: 'delete',
                                class: 'layui-btn layui-btn-xs layui-btn-danger',
                                extend: 'data-full="true"',
                            }
                        ]]
                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(house)',function(data){
                var houseid=data.value;
                var cdate=$("#cdate").val();
                getBatchDays(houseid,cdate);
            });
            var laydate=layui.laydate;
            laydate.render({
                elem: '#cdate'
                ,done: function(value, date, endDate){
                    console.log(value); //得到日期生成的值，如：2017-08-18
                    console.log(date); //得到日期时间对象：{year: 2017, month: 8, date: 18, hours: 0, minutes: 0, seconds: 0}
                    console.log(endDate); //得结束的日期时间对象，开启范围选择（range: true）才会返回。对象成员同上。
                    var houseid = $("#houseid").val();
                    if(houseid==''){
                        layer.alert('未选择鸡舍！');
                        return false;
                    }
                    var cdate = value;
                    getBatchDays(houseid,cdate);
                }
            });
            ea.listen();
        },
        edit: function () {
            var form=layui.form;
            form.on('select(house)',function(data){
                var houseid=data.value;
                var cdate=$("#cdate").val();
                getBatchDays(houseid,cdate);
            });
            var laydate=layui.laydate;
            laydate.render({
                elem: '#cdate'
                ,done: function(value, date, endDate){
                    console.log(value); //得到日期生成的值，如：2017-08-18
                    console.log(date); //得到日期时间对象：{year: 2017, month: 8, date: 18, hours: 0, minutes: 0, seconds: 0}
                    console.log(endDate); //得结束的日期时间对象，开启范围选择（range: true）才会返回。对象成员同上。
                    var houseid = $("#houseid").val();
                    if(houseid==''){
                        layer.alert('未选择鸡舍！');
                        return false;
                    }
                    var cdate = value;
                    getBatchDays(houseid,cdate);
                }
            });

            ea.listen();
        },
    };
    function getBatchDays(houseid,cdate){
        console.log(houseid+'*****'+cdate);
        $.ajax({
            url:"getBatchDays",
            data:{
                'houseid':houseid,
                'cdate':cdate
            },
            type:"post",
            dataType:"json", //设置接受到的响应数据的格式
            success:function (res){
                console.log(res)
                if(res.code==200){
                    var data=res.data;
                    $("#batchname").val(data.batchname);
                    $("#days").val(data.days);
                    $("#cunlan").val(data.cunlan);
                    $("#batch_id").val(data.batch_id);
                }else{
                    layer.alert("未找到数据!");
                }
            },
            error:function () {
                //alert("出错了");
                console.log("出错了");
            },//表示如果请求响应出现错误，会执行的回调函数

        })
    }
    return Controller;
});