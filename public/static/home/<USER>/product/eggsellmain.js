define(["jquery", "easy-admin", "znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.eggsellmain/index',
        add_url: 'product.eggsellmain/add',
        edit_url: 'product.eggsellmain/edit',
        delete_url: 'product.eggsellmain/delete',
        export_url: 'product.eggsellmain/export',
        modify_url: 'product.eggsellmain/modify',
        sellprint_url: 'product.eggsellmain/sellprint',
        search_url: 'product.eggsellmain/search',
    };
    var index = Number($('#index').val());
    var munit = $('#munit').val();
    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory.factoryname', title: '厂区'},
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'housename', title: '鸡舍'},
                    {field: 'cdate', title: '日期'},
                    {field: 'totalNum',title:'合计数量（枚）',search: false},
                    {field: 'amount', title: '金额',search:false},
                    {field: 'custom.unitname', title: '收购单位',search:false,templet:function (d){
                        return d.custom_id==0?'':d.custom.unitname;
                        }},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印销售单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });

        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        search:function(){
            var cols=[[
                {field: 'sale_no', title: '销售单号',align:"center"},
                {field: 'housename', title: '鸡舍',align:"center"},
                {field: 'cdate', title: '销售日期',align:"center"},
                {field: 'num', title: '数量(枚)',align:"center"},
                {field: 'weight', title: '重量('+munit+')',align:"center"},
                {field: 'price', title: '单价(元）',align:"center"},
                {field: 'amount', title: '金额(元)',align:"center"},
                {field: 'unitName', title: '收购单位',align:"center"},
                {field: 'remark', title: '备注',align:"center"},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },
        sellprint:function (){
            ea.listen();
        },
        houseview:function(){
            ea.listen();
        }
    };
    var form=layui.form;
    $('#add').on('click',function (){
        index++;
        console.log(index);
        var house_id = 'houseid'+index;
        var class_id = 'class'+index;
        var div_id = 'div'+index;
        var num_id = 'num'+index;
        var weight_id = 'weight'+index;
        var amount_id = 'amount'+index;
        var price_id = 'price'+index;
        var price = $('#price1').val();
        var trdata = '<tr id="' + div_id + '">' +
            '<td id="house"><select name="prod_id[]" lay-verify="required" id="'+house_id+'"></select></td>' +
            '<td id="type"><select name="class_id[]" lay-verify="required" id="'+class_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="'+num_id+'" placeholder="枚数"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="weight[]" id="'+weight_id+'" placeholder="'+munit+'"  value="" onblur="setTotal('+index+',1)"/></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="'+price_id+'" placeholder="单价"  onblur="setTotal('+index+',1)"  value="'+price+'"/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="'+amount_id+'" value="" placeholder="请输入金额" onblur="setTotal('+index+',2)"  /></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        //$('#table').append(trdata);
        $('#total').before(trdata);
        createSelect('houseid1',house_id);
        createSelect('class1',class_id);
        form.render('select');
    })

    function createSelect(sourceid,selectid) {
        var source = document.getElementById(sourceid);
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }


    return Controller;
});