define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.ymset/index',
        add_url: 'product.ymset/add',
        edit_url: 'product.ymset/edit',
        delete_url: 'product.ymset/delete',
        ymbuild_url: 'product.ymset/ymbuild',
        modify_url: 'product.ymset/modify',
    };

    var Controller = {

        index: function () {
            var id=getQueryVariable("id");
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url+'?id='+id,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }],

                    [{
                        text: '重新生成',
                        url: init.ymbuild_url+'?id='+id,
                        method: 'request',
                        auth: 'ymbuild',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-pencil ',
                        title:'该操作将对本批鸡舍重新生成，原数据将被覆盖！',
                        extend: 'data-full="false"',
                    }] ,'delete'],
                where: {id: id},
                cols: [[
                    {type: 'checkbox'},
                    {field: 'days', title: '日龄'},
                    {field: 'cdate', title: '接种日期',search:'range'},
                    {field: 'ymname', title: '疫苗名称'},
                    {field: 'num', title: '剂量',search: false},
                    {field: 'method', title: '免疫方法',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var id=getQueryVariable("id");
            ea.listen(function(data){
                data.ym_id = id;
                console.log(id);
                return data;
            });
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});


//接收页面传值的方法
function getQueryVariable(variable){
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
        var pair = vars[i].split("=");
        if(pair[0] == variable){return pair[1];}
    }
    return(false);
}