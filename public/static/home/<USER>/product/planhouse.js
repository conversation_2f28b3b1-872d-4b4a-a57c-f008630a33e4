define(["jquery", "easy-admin","miniTab","znegdoo"], function ($, ea,miniTab,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.planhouse/index',
        add_url: 'product.planhouse/add',
        edit_url: 'product.planhouse/edit',
        delete_url: 'product.planhouse/delete',
        export_url: 'product.planhouse/export',
        modify_url: 'product.planhouse/modify',
    };

    var Controller = {
        index: function () {
            miniTab.listen();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区'},
                    {field: 'houseprod.house.housename', title: '鸡舍'},
                    {field: 'batchname', title: '批次',search:false},
                    {field: 'houseprod.status', title: '状态',search: 'select', selectList: {"0": "已淘","1": "在用", },templet:function (d){
                            return d.houseprod.status==0?'已淘':'在用'
                        }},
                    {
                        width: 250, title: '操作', templet:ea.table.tool,
                        operat: [
                            [{
                                text: '编辑生产计划',
                                extra:'name',
                                url: '/home/<USER>/index',
                                method: 'newpage',//自定义执行方法，easy_admin.js,line:463
                                auth: 'add',
                                field:'id',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: '',
                            },{
                                text: '克隆',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="false"',
                            }
                            ],'delete']
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            ea.listen();
        },
        edit: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            ea.listen();
        },
    };
    return Controller;
});