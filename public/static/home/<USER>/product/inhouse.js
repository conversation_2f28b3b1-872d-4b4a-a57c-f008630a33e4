define(["jquery", "easy-admin","znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.inhouse/index',
        add_url: 'product.inhouse/add',
        edit_url: 'product.inhouse/edit',
        delete_url: 'product.inhouse/delete',
        export_url: 'product.inhouse/export',
        modify_url: 'product.inhouse/modify',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '分配鸡舍',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: "checkbox"},
                    {field: 'batch.batchname', minWidth: 80, title: '批次'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,templet: function (d){
                            return d.factory;
                        }},
                    {field: 'house.housename', minWidth: 80, title: '鸡舍'},
                    {field: 'inhouse_time', minWidth: 80, title: '入舍时间', search: 'range'},
                    {field: 'inhouse_num', minWidth: 80, title: '入舍数量', search: false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat: ['delete'],
                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(batch_id)', function (data) {
                $.ajax({
                    url:'../Common/getBatchNum',
                    data:{
                        batch_id:data.value
                    },
                    type:'get',
                    dataType:'json',
                    success:function (res){
                        console.log(res);
                        $('#sellnum').val(res);
                    }
                })
            });
            form.on('select(factory_id)',function(data){
                getHouse(data.value,form);
            })

            form.on('select(house)',function (data){
                $.ajax({
                    url:'../Common/getHouseCunlanReal',
                    data:{
                        houseid:data.value
                    },
                    type:'get',
                    dataType:'json',
                    success:function (res){
                        console.log(res);
                        $('#cunlan').val(res);
                    }
                })
            });
            $('#inhouse_num').blur(function (data) {
                var _this = $(this);
                var v1 = parseInt(_this.val());
                var v2 = parseInt($('#sellnum').val());
                if (v1 > v2) {
                    layer.alert('超出分配范围！');
                    _this.val('');
                }
                var sh = parseInt($('#sellnum').val()) - parseInt($('#inhouse_num').val());
                var msg = '该批次分配数量为' + $('#inhouse_num').val() + ',剩余数量' + sh + ',请仔细检查是否正确！';
                layer.alert(msg);
            });

            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.inhouse_time));
                data.inhouse_time = date.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            var form = layui.form;
            form.on('select(batch_id)', function (data) {
                $('#sellnum').val(data.elem[data.elem.selectedIndex].dataset.val);
                $('#inhouse_num').val(data.elem[data.elem.selectedIndex].dataset.val);
            });
            $('#inhouse_num').blur(function (data) {
                var _this = $(this);
                var v1 = parseInt(_this.val());
                var v2 = parseInt($('#sellnum').val());
                if (v1 > v2) {
                    layer.alert('超出分配范围！');
                    _this.val('');
                }
            });

            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.inhouse_time));
                data.inhouse_time = date.getTime() / 1000;
                return data;
            });
        },
    };

//厂区鸡舍联动，获取物理鸡舍数据
    function getHouse(val, form) {
        $.ajax({
            url: '../Common/getHouseReal',
            data: {
                factory_id: val
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                console.log(res);
                $('#houseid').empty();
                $('#houseid').append('<option>请选择鸡舍……</option>');
                res.forEach(function (e) {
                    $('#houseid').append('<option value="' + e.id + '">' + e.housename + '</option>');
                })
                form.render();
            }
        })
    }

    return Controller;
});