define(["jquery", "easy-admin", "znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.layergrow/index',
        add_url: 'product.layergrow/add',
        edit_url: 'product.layergrow/edit',
        delete_url: 'product.layergrow/delete',
        export_url: 'product.layergrow/export',
        modify_url: 'product.layergrow/modify',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,searchOp:"=",templet: function (d){
                            return d.factory;
                        }},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'weeks', title: '周龄'},
                    {field: 'num', title: '测量数量',search:false},
                    {field: 'weight_qua', title: '体重达标率(%)',search:false},
                    {field: 'weight_even', title: '体重均匀度(%)',search:false},
                    {field: 'shin_qua', title: '胫长达标率(%)',search:false},
                    {field: 'weight_upper_num', title: '高于体重上限数',search:false},
                    {field: 'weight_lower_num', title: '低于体重上限数',search:false},
                    {field: 'cdate', title: '采集日期',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            //日期转为时间戳
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;

            });
        },
        edit: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            //日期转为时间戳
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;

            });
        },
    };
    return Controller;
});