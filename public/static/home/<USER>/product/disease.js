define(["jquery", "easy-admin","znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.disease/index',
        add_url: 'product.disease/add',
        edit_url: 'product.disease/edit',
        delete_url: 'product.disease/delete',
        export_url: 'product.disease/export',
        record_url: 'product.disease/record',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,templet: function (d){
                            return d.factory;
                        }},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'cdate', title: '发病日期',search: false},
                    {field: 'days', title: '日龄',search:false},
                    {field: 'title', title: '发病名称'},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '治疗记录',
                                url: init.record_url,
                                method: 'open',
                                auth: 'record',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            }
                        ],'edit','delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
    };
    return Controller;
});