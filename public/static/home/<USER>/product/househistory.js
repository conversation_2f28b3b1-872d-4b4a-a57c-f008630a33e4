define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.househistory/index',
        add_url: 'product.househistory/add',
        edit_url: 'product.househistory/edit',
        delete_url: 'product.househistory/delete',
        export_url: 'product.househistory/export',
        modify_url: 'product.househistory/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区'},
                    {field: 'batch.batchname', title: '批次'},
                    {field: 'house.housename', title: '鸡舍名称'},
                    {field: 'typename', title: '品种',search: false},
                    {field: 'inhouse_time', title: '入栏时间'},
                    {field: 'cunlan', title: '入栏存栏',search: false},
                    //{field: 'out_time', title: '出栏时间'},

                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
        chulan:function (){
            ea.listen();
        },
        taotai:function (){
            ea.listen();
        }
    };
    return Controller;
});