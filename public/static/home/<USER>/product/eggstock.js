define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.eggstock/index',
        add_url: 'product.eggstock/add',
        edit_url: 'product.eggstock/edit',
        delete_url: 'product.eggstock/delete',
        export_url: 'product.eggstock/export',
        print_url: 'product.eggstock/print',
        view_url: 'product.eggstock/view',
        search_url: 'product.eggstock/search',
        total_url: 'product.eggstock/total',
        import_url: 'product.eggstock/import',
    };
    var init_stock = {
        table_elem: '#currentTable_stock',
        table_render_id: 'currentTableRenderId_stock',
        index_url: 'product.eggstock/stock',

    };
    var index = Number($('#index').val());
    var munit = $('#munit').val();
    var Controller = {
        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'search',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    },{
                        text: '入库统计',
                        url: init.total_url,
                        method: 'open',
                        auth: 'edit',
                        class: 'layui-btn layui-bg-blue layui-btn-sm',
                        icon: 'fa fa-level-down ',
                        extend: 'data-full="true"',
                    }
                    // ,{
                    //     text: '一键入库',
                    //     url: init.import_url,
                    //     method: 'open',
                    //     auth: 'add',
                    //     class: 'layui-btn layui-bg-green layui-btn-sm',
                    //     icon: 'fa fa-level-down',
                    //     extend: 'data-full="false"',
                    // }
                    ],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '入库单号'},
                    {field: 'factory.factoryname', title: '厂区'},
                    {field: 'num', title: '合计数量（枚）',search:false},
                    {field: 'weight', title: '合计重量('+munit+')',search:false},
                    {field: 'cdate', title: '入库日期',search:'range'},
                    {field: 'worker.name', title: '库管员',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印入库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;


            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        view:function(){
            ea.listen();
        },
        stock:function(){

            ea.table.render({
                init: init_stock,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区',search:false},
                    {field: 'housename', title: '鸡舍',search:false},
                    {field: 'total_num', title: '总数量（枚）',search:false},
                    {field: 'total_weight', title: '总重量('+munit+')',search:false},
                    {field: 'sell_num', title: '出库数量（枚）',search:false},
                    {field: 'sell_weight', title: '出库重量('+munit+')',search:false},
                    {field: 'stock_num', title: '库存数量（枚）',search:false},
                    {field: 'stock_weight', title: '库存重量',search:false},
                ]],
            });
            ea.listen();
        },
        print:function(){
            ea.listen();
        },
        search:function(){
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })

            var munit = munit2;
            var cols=[[
            {field: 'no', title: '单号',align:"center"},
            {field: 'housename', title: '鸡舍',align:"center"},
            {field: 'eggtype', title: '品种',align:"center"},
            {field: 'cdate', title: '日期',align:"center"},
            {field: 'normal_num', title: '合格蛋(枚)',align:"center"},
            {field: 'normal_weight', title: '合格蛋('+munit+')',align:"center"},
            {field: 'double_num', title: '双黄蛋(枚)',align:"center"},
            {field: 'double_weight', title: '双黄蛋('+munit+')',align:"center"},
            {field: 'defective_num', title: '次品蛋(枚)',align:"center"},
            {field: 'defective_weight', title: '次品蛋('+munit+')',align:"center"},
            {field: 'broken_num', title: '破蛋(枚)',align:"center"},
            {field: 'broken_weight', title: '破蛋('+munit+')',align:"center"},
            {field: 'total_num', title: '合计(枚)',align:"center"},
            {field: 'total_weight', title: '合计('+munit+')',align:"center"},
        ]];
            zn.doSearch(cols);
            ea.listen();
        },
        total:function(){
            var cols=[[
                {field: 'sort', title: '排序',width:80,align:"center",sort:true},
                {field: 'housename', title: '鸡舍',align:"center",totalRowText: '合计：'},
                {field: 'normal_num', title: '合格蛋(枚)',align:"center",totalRow: true},
                {field: 'normal_weight', title: '合格蛋('+munit+')',align:"center",totalRow: true},
                {field: 'double_num', title: '双黄蛋(枚)',align:"center",totalRow: true},
                {field: 'double_weight', title: '双黄蛋('+munit+')',align:"center",totalRow: true},
                {field: 'defective_num', title: '次品蛋(枚)',align:"center",totalRow: true},
                {field: 'defective_weight', title: '次品蛋('+munit+')',align:"center",totalRow: true},
                {field: 'broken_num', title: '破蛋(枚)',align:"center",totalRow: true},
                {field: 'broken_weight', title: '破蛋('+munit+')',align:"center",totalRow: true},
                {field: 'total_num', title: '合计(枚)',align:"center",totalRow: true},
                {field: 'total_weight', title: '合计('+munit+')',align:"center",totalRow: true},
            ]];
            $('#do_total').on('click', function () {
                // 加载表格
                var factory_id = $('#factory_id').val();
                var cdate = $('#cdate').val();
                layui.use(['table', 'jquery', 'layer'], function () {
                    var table = layui.table;
                    var $ = layui.jquery;
                    table.render({
                        // 表格容器ID
                        elem: '#totalTable'
                        , totalRow: true // 开启合计行
                        // 表格ID，必须设置，重载部分需要用到
                        , id: 'totalTable_filter'
                        // 数据接口链接
                        , url: "total"
                        // 附加参数，post token
                        , where: {
                            'factory_id': factory_id,
                            'cdate': cdate
                        }
                        , method: 'post'
                        // 分页 curr起始页，groups连续显示的页码，limit默认每页显示的条数
                        , page: {
                            layout: ['prev', 'page', 'next', 'skip', 'count', 'limit']
                            , curr: 1
                            , groups: 6
                            , limit: 20
                        }
                        , cols: cols
                    });
                });
            })
            ea.listen();
        },
        import:function(){
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        }
    };
    var form=layui.form;
    $('#add').on('click',function (){
        if($('#factory_id').val()==''){
            layer.alert('请先选择厂区！');
            return false;
        }
        index++;
        console.log(munit);
        var house_id = 'houseid'+index;
       //var type_id = 'type'+index;
        var div_id = 'div'+index;
        var normal_num_id = 'normal_num'+index;
        var normal_weight_id = 'normal_weight'+index;
        var double_num_id = 'double_num'+index;
        var double_weight_id = 'double_weight'+index;
        var defective_num_id = 'defective_num'+index;
        var defective_weight_id = 'defective_weight'+index;
        var broken_num_id = 'broken_num'+index;
        var broken_weight_id = 'broken_weight'+index;
        var total_num_id = 'total_num'+index;
        var total_weight_id = 'total_weight'+index;
        var type = 'type'+index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id="house"><select name="prod_id[]" lay-verify="required" id="'+house_id+'"></select></td>' +
            //'<td id="type"><select name="type_id[]" lay-verify="required" id="'+type_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="normal_weight[]" id="'+normal_weight_id+'" placeholder="'+munit+'"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="normal_num[]" id="'+normal_num_id+'" placeholder="枚"  value="" onblur="setTotal('+index+')" /></td>' +
            '<td><input type="text" class="layui-input" name="double_weight[]" id="'+double_weight_id+'" placeholder="'+munit+'"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="double_num[]" id="'+double_num_id+'" placeholder="枚"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="defective_weight[]" id="'+defective_weight_id+'" placeholder="'+munit+'"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="defective_num[]" id="'+defective_num_id+'" placeholder="枚"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="broken_weight[]" id="'+broken_weight_id+'" placeholder="'+munit+'"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="broken_num[]" id="'+broken_num_id+'" placeholder="枚"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="total_weight[]" id="'+total_weight_id+'" placeholder="'+munit+'"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="total_num[]" id="'+total_num_id+'" placeholder="枚"  onblur="setTotal('+index+')" value="" /></td>' +
            '<td>' +
            '<input type="hidden" id="'+type+'" value="0"><button type="button" id="import1" class="layui-btn layui-btn-sm"  value="' + index + '">导入\n' +
            '</button><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#total').before(trdata);
        zn.createSelect('houseid1',house_id);
        //createSelect('type1',type_id);
        form.render('select');
    })

    function showNote(){
        var form=layui.form;
        form.on('select(batch_id)',function(data){
            $.ajax({
                type:'post',
                url:'getEggNote',
                data:{
                    batch_id:data.value
                },
                dataType:'json',
                success:function(res){
                    console.log(res);
                    let note = res.data;
                    $('#note').html(note);
                }
            })
        })
    }
    function getHouseProd(val,form){
        $.ajax({
            url:'../Common/getHouseProd',
            data:{
                factory_id:val
            },
            type:'get',
            dataType: 'json',
            success: function (res) {
                console.log(res);
                $('#prod_id').empty();
                $('#prod_id').append('<option>请选择……</option>');
                res.forEach(function (e){
                    $('#prod_id').append('<option value="'+e.id+'">'+e.housename+'</option>');
                })
                form.render();
            }
        })
    }

    // 监听所有按钮的点击事件
    $(document).on('click', 'button', function() {
        var index = $(this).attr('value');
        var cdate = $('#cdate').val();
        var prod_id = $('#houseid'+index).val();
        console.log(index+','+cdate+','+prod_id);
        $.ajax({
            type:'post',
            url:'getEggNum',
            data:{
                cdate:cdate,
                prod_id:prod_id
            },
            dataType:'json',
            success:function(res){
                console.log(res);
                if(res.code==200){
                    let num = res.data;
                    $('#normal_num'+index).val(num);
                    $('#total_num'+index).val(num);
                    $('#type'+index).val(1);
                    $('#normal_num'+index).prop('readonly',true);
                    $('#normal_num'+index).css('background-color','#91d2e4');
                    $('#total_num'+index).css('background-color','#91d2e4');
                    setTotal(index);
                }else{
                    layer.alert('没有获取到计蛋器数据或者计数为0！');
                    $('#normal_num'+index).val('');
                    $('#total_num'+index).val('');
                    $('#type'+index).val(0);
                    $('#normal_num'+index).prop('readonly',false);
                    $('#normal_num'+index).css('background-color','#ffffff');
                    $('#total_num'+index).css('background-color','#ffffff');
                }

            }
        })
    });
    return Controller;
});