define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.hclear/index',
        add_url: 'product.hclear/add',
        edit_url: 'product.hclear/edit',
        delete_url: 'product.hclear/delete',
        export_url: 'product.hclear/export',
        modify_url: 'product.hclear/modify',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '鸡舍清空登记',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }]],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,templet: function (d){
                            return d.factory;
                        }},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'batchname', title: '批次',search:false},
                    {field: 'cdate', title: '清空时间',search: false},
                    {
                        width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '恢复',
                                url: init.delete_url,
                                method: 'request',
                                auth: 'delete',
                                class: 'layui-btn layui-btn-xs layui-btn-danger',
                                extend: 'data-full="true"',
                            }
                        ]]
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(house)', function (data) {
                getCunlan(data.value);
            })
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            var form = layui.form;
            form.on('select(house)', function (data) {
                getCunlan(data.value);
            })
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
    };

    function getCunlan(prod_id) {
        $.ajax({
            url: '../Common/getHouseCunlan',
            data: {
                prod_id: prod_id
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                console.log(res);
                layer.msg('当前存栏数量为:' + res + ',请谨慎操作！');
            }
        })
    }

    return Controller;
});