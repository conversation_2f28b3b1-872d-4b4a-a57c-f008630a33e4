define(["jquery", "easy-admin", "znegdoo", "echarts", "echarts-theme",], function ($, ea, zn, echarts, undefined) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.eggcharts/index',

    };
    var Controller = {

        index: function () {
            // 发送AJAX请求

            var form = layui.form;
            $('#findBtn').on('click', function () {
                load();
            })
            $('#xqBtn').on('click', function () {
                table_a();
            })
            $('#customXqBtn').on('click', function () {
                table_custom();
            })
            setTimeout(res => {
                load();
            }, 500)
            ea.listen();
        },
        add: function () {

        },
        edit: function () {

        },

    };

    //显示图表-饼图
    function pieEcharts(data) {
        var title = data.title;
        var idname = data.name;
        var dataVal = data.data;
        console.log('idname:', idname);
        var chartDom = document.getElementById(idname);
        var myChart = echarts.init(chartDom);
        var option;


        option = {
            title: {
                text: title,
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                top: 'bottom'
            },
            series: [
                {
                    name: 'Access From',
                    type: 'pie',
                    radius: '50%',
                    data: dataVal,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };

        option && myChart.setOption(option);

        window.addEventListener("resize", function () {
            echartsRecords.resize();
        });
    }

    //图表 每月销售量、销售额折线图
    function monthCharts(idname, title, data) {
        var chartDom = document.getElementById(idname);
        var myChart = echarts.init(chartDom);
        var option;
        var xAxis = data.xAxis;
        var series = data.series;
        var legend = data.legend;
        option = {
            title: {
                text: title
            },
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    var res = `${params[0].name} <br/>`
                    for (const item of params) {
                        if (item.value !== 0) {
                            res += `<span style="background: ${item.color}; height:10px; width: 10px; border-radius: 50%;display: inline-block;margin-right:10px;"></span> ${item.seriesName} ：${item.value}<br/>`
                        }
                    }
                    return res
                }
            },
            legend: {
                top: '0',
                data: legend,
                type: 'scroll',
                orient: 'horizontal' // vertical

            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xAxis
            },
            yAxis: {
                type: 'value'
            },
            series: series
        };

        option && myChart.setOption(option);

    }

    /** 销售详情统计 */
    function table_a() {
        var table = layui.table;
        var year = $('#year').val();
        var factory_id = $('#factory_id').val();
        var cdate = $('#cdate').val();
        console.log('CDATE:', cdate);
        //执行渲染
        table.render({
            elem: '#xq' //指定原始表格元素选择器（推荐id选择器）
            , id: 'xq'
            , height: 315 //容器高度
            , url: 'xqList'
            , where: {
                year: year,
                factory_id: factory_id,
                cdate: cdate
            }
            , cols: [[
                {field: 'housename', title: '鸡舍', minWidth: 80},
                {field: 'normal', title: '合格蛋', minWidth: 80},
                {field: 'double', title: '双黄蛋', minWidth: 80},
                {field: 'defective', title: '次品蛋', minWidth: 80},
                {field: 'broken', title: '破蛋', minWidth: 80},
                {field: 'total', title: '合计', minWidth: 80},
            ]] //设置表头

        });
    }

    /** 客户详情统计 */
    function table_custom() {
        var table = layui.table;
        var year = $('#year').val();
        var factory_id = $('#factory_id').val();
        var cdate = $('#cdate2').val();
        console.log('CDATE:', cdate);
        //执行渲染
        table.render({
            elem: '#customXq' //指定原始表格元素选择器（推荐id选择器）
            , id: 'customXq'
            , height: 315 //容器高度
            , url: 'customXqList'
            , where: {
                year: year,
                factory_id: factory_id,
                cdate: cdate
            }
            , cols: [[
                {field: 'customname', title: '客户名称', minWidth: 80},
                {field: 'normal', title: '合格蛋', minWidth: 80},
                {field: 'double', title: '双黄蛋', minWidth: 80},
                {field: 'defective', title: '次品蛋', minWidth: 80},
                {field: 'broken', title: '破蛋', minWidth: 80},
                {field: 'total', title: '合计', minWidth: 80},
            ]] //设置表头

        });
    }

    /** 加载主要图表 */
    function load() {
        const year = $('#year').val();
        const factory_id = $('#factory_id').val();
        const timestamp = Date.now();
        console.log('开始：',timestamp);
        $.ajax({
            url: 'index', // 替换为你的API端点
            data: {
                year: year,
                factory_id: factory_id
            },
            type: 'post', // 或者 'POST'，根据需要
            dataType: 'json', // 期望从服务器返回的数据类型
            success: function (res) {
                // 请求成功后的回调函数
                const timestamp2 = Date.now();
                console.log('结束：',timestamp2);
                // 处理返回的数据，比如更新页面内容
                let pieNumData = res.pieNumData;
                let pieWeightData = res.pieWeightData;
                let pieAmountData = res.pieAmountData;
                //pieEcharts(pieNumData);
                //pieEcharts(pieWeightData);
                pieEcharts(pieAmountData);

                let houseWeightData = res.houseWeightList;
                let houseAmountData = res.houseAmountList;
                monthCharts('weightChart', '', houseWeightData);
                monthCharts('amountChart', '', houseAmountData);

                let customData = res.customData;
                monthCharts('customChart', '', customData);

                let priceData = res.priceData;
                priceChart('priceChart',priceData);

            },
            error: function (xhr, status, error) {
                // 请求失败的回调函数
                console.error('Error:', error);
            }
        });
        table_a();
        table_custom();
    }
    /** 月价格变化 - 柱状图*/
    function priceChart(idname,data){
        var chartDom = document.getElementById(idname);
        var myChart = echarts.init(chartDom);
        var option;
        var xAxis = data.xAxis;
        var series = data.series;
        option = {
            title: {
                text: '价格变化'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {},
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis:  {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: xAxis,
            },
            series: series
        };

        option && myChart.setOption(option);

    }
    return Controller;
});