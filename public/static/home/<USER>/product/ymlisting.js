define(["jquery", "easy-admin", "miniTab","znegdoo"], function ($, ea, miniTab,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.ymlisting/index',
        add_url: 'product.ymlisting/add',
        edit_url: 'product.ymlisting/edit',
        delete_url: 'product.ymlisting/delete',
        export_url: 'product.ymlisting/export',
    };

    var Controller = {
        index: function () {
            miniTab.listen();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区',search:false},
                    {field: 'houseprod.house.housename', title: '鸡舍'},
                    {field: 'batchname', title: '批次',search:false},
                    {field: 'houseprod.status', title: '状态',search: 'select', selectList: {"0":"已淘","1":"在用"},templet:function (d){
                        return d.houseprod.status==0?'已淘':'在用'
                        }},
                    {field: 'create_time', title: '生成时间',search:false},
                    {
                        width: 250, title: '操作', templet: function (data) {
                            var id = data.id;
                            var btn = "";
                            btn += '<a target="_self" layuimini-content-href="/home/<USER>/index?id=' + id + '" data-title="' + data.houseprod.house.housename + '疫苗程序" class="layui-btn layui-btn-xs" href="javascript:void(0)">编辑疫苗程序</a>';
                            return btn;
                        }
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },

    };
    return Controller;
});



