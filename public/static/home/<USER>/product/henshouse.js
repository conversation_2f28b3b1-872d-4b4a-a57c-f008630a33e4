define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.henshouse/index',
        add_url: 'product.henshouse/add',
        edit_url: 'product.henshouse/edit',
        delete_url: 'product.henshouse/delete',
        export_url: 'product.henshouse/export',
        modify_url: 'product.henshouse/modify',
        taotai_url: 'product.henshouse/taotai',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: "checkbox"},
                    {field: 'factory.factoryname', width: 120, title: '厂区', search: false},
                    {field: 'housename', minWidth: 150, title: '鸡舍名称'},
                    {field: 'sort', width: 80, title: '序号', edit: 'text'},
                    {field: 'lb', Width: 80, title: '类别', selectList: {1: '蛋鸡舍', 2: '雏鸡舍', 3: '肉鸡舍',0:'其它'}},
                    {field: 'status', title: '状态', width: 85, selectList: {0: '<span class="layui-badge layui-bg-green">空闲</span>', 1: '<span class="layui-badge">在用</span>'}, search: 'select'},
                    {field: 'size', title: '设计容量', search: false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,

                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.verify({
                integer:[/^[0-9]*$/,"只能填写数字"],
            });
            ea.listen();
        },
        edit: function () {
            var form = layui.form;
            form.verify({
                integer:[/^[0-9]*$/,"只能填写数字"],
            });
            ea.listen(function (data) {
                if (data.isfengbi == 'on') {
                    data.isfengbi = '1';
                } else {
                    data.isfengbi = '0';
                }
                if (data.isnew == 'on') {
                    data.isnew = 1;
                } else {
                    data.isnew = 0;
                }
                return data;
            });
        },
        taotai: function () {
            ea.listen();
        },
    };
    return Controller;
});