define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.managepay/index',
        add_url: 'product.managepay/add',
        edit_url: 'product.managepay/edit',
        delete_url: 'product.managepay/delete',
        export_url: 'product.managepay/export',
        modify_url: 'product.managepay/modify',
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory();
            var houseList = zn.getHouseList();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,templet: function (d){
                            return d.factory;
                        }},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'itemname', search: 'select', selectList: {"1":"水费","2":"电费","3":"燃煤","4":"天然气","5":"工资","6":"设备维修","7":"均摊成本","0":"其它"}, title: '支出项'},
                    {field: 'cdate', title: '支出日期',search:'range'},
                    {field: 'num', title: '数量',templet: function (d){
                            return d.num+d.munit;
                        },search:false},
                    {field: 'amount', title: '金额',templet:ea.table.price,search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });

        },
        edit: function () {
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value,form);
            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
    };
    return Controller;
});