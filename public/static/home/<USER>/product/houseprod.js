define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.houseprod/index',
        add_url: 'product.houseprod/add',
        edit_url: 'product.houseprod/edit',
        delete_url: 'product.houseprod/delete',
        export_url: 'product.houseprod/export',
        modify_url: 'product.houseprod/modify',
        chulan_url: 'product.houseprod/chulan',
        taotai_url: 'product.houseprod/taotai',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区'},
                    {field: 'batch.batchname', title: '当前批次'},
                    {field: 'house.housename', title: '鸡舍名称'},
                    {field: 'typename', title: '品种',search: false},
                    {field: 'days', title: '日龄',search: false},
                    {field: 'cunlan', title: '入栏存栏',search:false},
                    {field: 'inhouse_time', title: '入栏时间',search:'range'},
                    {field: 'currCunlan', title: '当前存栏',search:false},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
        chulan:function (){
            ea.listen();
        },
        taotai:function (){
            ea.listen();
        }
    };
    return Controller;
});