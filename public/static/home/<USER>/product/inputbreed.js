define(["jquery", "easy-admin","znegdoo"], function ($, ea , zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'product.inputbreed/index',
        add_url: 'product.inputbreed/add',
        edit_url: 'product.inputbreed/edit',
        delete_url: 'product.inputbreed/delete',
        export_url: 'product.inputbreed/export',
        modify_url: 'product.inputbreed/modify',
        breeddata_url: 'product.inputbreed/getBreedData'
    };

    var Controller = {

        index: function () {
            var factoryList = zn.getFactory(1);
            var houseList = zn.getHouseList();
            const egg_unit = $('#egg_unit').val();
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '今日数据录入',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_id', minWidth:80,title: '厂区',selectList:factoryList,templet: function (d){
                        return d.factory;
                        }},
                    {field: 'prod_id', minWidth:80,title: '鸡舍名称',selectList:houseList,templet: function (d){
                            return d.housename;
                        }},
                    {field: 'prod_date', title: '日期',search:'range'},
                    {field: 'typename', title: '品种',search: false},
                    {field: 'days', title: '日龄',search: false},
                    {field: 'cunlan', title: '存栏',search: false},
                    //{field: 'dead_num', title: '死淘数量',search: false},
                   // {field: 'weight', title: '平均体重(g)',search: false},
                    //{field: 'feednum', title: '用料量(kg)',search: false},
                    //{field: 'eggweight', title: '产蛋量(kg)',search: false},
                    {field: 'eggnum', title: '产蛋数(枚)',search: false},
                    {field: 'eggweight', title: '产蛋量('+egg_unit+')',search: false},
                    //{field: 'egglu', title: '产蛋率(%)',search: false},
                    {field: 'feedeggbi', title: '料蛋比',search: false},
                    //{field: 'powernum', title: '用电量',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen(function (data){
                console.log(data);
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            //日期转为时间戳
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.prod_date));
                data.prod_date=date.getTime()/1000;
                return data;

            });

        },
        add:function (){
            var form=layui.form;
            form.on('select(house)',function(data){
                getBreedData();
            });
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            var laydate=layui.laydate;
            laydate.render({
                elem:'#prod_date',//制定元素
                type:'date',
                //range:true,//开启左右面板
                min:'2017-09-1',//
                max:Date.now(),//规定时间期限
                done:function(value,date){//value, date, endDate点击日期、清空、现在、确定均会触发。回调返回三个参数，分别代表：生成的值、日期时间对象、结束的日期时间对象
                    console.log(value);
                    console.log(date);
                    getBreedData();
                }
            });
            //日期转为时间戳
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.prod_date));
                data.prod_date=date.getTime()/1000;
                return data;
            });
        }
    };

    function getBreedData(){
        var prod_id=$('#prod_id').val();
        var prod_date=$('#prod_date').val();
        console.log(prod_date);
        $.ajax({
            url:'getBreedData',
            data:{
                prod_id:prod_id,
                prod_date:prod_date
            },
            type:'post',
            success:res=>{
                console.log(res);
                var data=res.data;
                if(data!=null){
                    document.getElementById('feednum').value=data.feednum || '';
                    document.getElementById('waternum').value=data.waternum || '';
                    document.getElementById('eggweight').value=data.eggweight || '';
                    document.getElementById('eggnum').value=data.eggnum || '';
                    //document.getElementById('eggweightavg').value=data.eggweightavg || '';
                    document.getElementById('badegg').value=data.badegg || '';
                    document.getElementById('goodegg').value=data.goodegg || '';
                    document.getElementById('dirtyegg').value=data.dirtyegg || '';
                    document.getElementById('whiteegg').value=data.whiteegg || '';
                    document.getElementById('powernum').value=data.powernum || '';
                    document.getElementById('days').value=data.days || '';
                }


            },
            error:function () {
                //alert("出错了");
                console.log("出错了");
            },//表示如果请求响应出现错误，会执行的回调函数
            dataType:"json" //设置接受到的响应数据的格式
        })
    }

    return Controller;
});