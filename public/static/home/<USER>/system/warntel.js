define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.warntel/index',
        add_url: 'system.warntel/add',
        edit_url: 'system.warntel/edit',
        delete_url: 'system.warntel/delete',
        export_url: 'system.warntel/export',
        modify_url: 'system.warntel/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {field: 'housename', title: '鸡舍'},
                    {field: 'sn', title: '设备编码'},
                    {field: 'tel1', title: '报警电话1'},
                    {field: 'tel2', title: '报警电话2'},
                    {field: 'tel3', title: '报警电话3'},
                    {field: 'status', title: '状态', templet: ea.table.switch},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});