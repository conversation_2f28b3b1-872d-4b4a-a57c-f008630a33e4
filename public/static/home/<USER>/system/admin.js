define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.admin/index',
        add_url: 'system.admin/add',
        edit_url: 'system.admin/edit',
        delete_url: 'system.admin/delete',
        modify_url: 'system.admin/modify',
        export_url: 'system.admin/export',
        password_url: 'system.admin/password',
    };

    var Controller = {

        index: function () {

            ea.table.render({
                init: init,
                cols: [[
                    {type: "checkbox"},
                    {field: 'username', minWidth: 80, title: '登录账户'},
                    {field: 'title', minWidth: 80, title: '角色',search: false},
                    // {field: 'head_img', minWidth: 80, title: '头像', search: false, templet: ea.table.image},
                    {field: 'login_num', minWidth: 80, title: '登录次数',search: false},
                    {field: 'workerName', minWidth: 80, title: '员工姓名'},
                    {field: 'status', title: '状态', width: 85, search: 'select', selectList: {0: '禁用', 1: '启用'}, templet: ea.table.switch},
                    {field: 'factoryname', minWidth: 80, title: '厂区范围',search:false},
                    {field: 'houserange', minWidth: 80, title: '鸡舍范围',search:false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat: [
                            'edit',
                            [{
                                text: '设置密码',
                                url: init.password_url,
                                method: 'open',
                                auth: 'password',
                                class: 'layui-btn layui-btn-normal layui-btn-xs',
                            }],
                            'delete'
                        ]
                    }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var tree = layui.tree;
            ea.request.get(
                {
                    url: 'getFactoryData',
                }, function (res) {
                    res.data = res.data || [];
                    console.log(res.data)
                    tree.render({
                        elem: '#factoryRange',
                        data: res.data,
                        showCheckbox: true,
                        id: 'rangeDataId',
                    });
                }
            );
            ea.listen(function (data) {
                var checkedData = tree.getChecked('rangeDataId');
                var factory = [];
                var house = [];
                $.each(checkedData, function (i, v) {
                    factory.push(v.id);
                    if (v.children !== undefined && v.children.length > 0) {
                        $.each(v.children, function (ii, vv) {
                            house.push(vv.id);
                        });
                    }
                });
                data.factoryRange = factory;
                data.houseRange = house;
                return data;
            });
        },
        edit: function () {
            var tree = layui.tree;
            var id = $('#id').val();
            ea.request.get(
                {
                    url: 'getFactoryData',
                    data:{id:id},
                }, function (res) {
                    res.data = res.data || [];
                    console.log(res.data)
                    tree.render({
                        elem: '#factoryRange',
                        data: res.data,
                        showCheckbox: true,
                        id: 'rangeDataId',
                    });
                }
            );
            ea.listen(function (data) {
                var checkedData = tree.getChecked('rangeDataId');
                var factory = [];
                var house = [];
                $.each(checkedData, function (i, v) {
                    factory.push(v.id);
                    if (v.children !== undefined && v.children.length > 0) {
                        $.each(v.children, function (ii, vv) {
                            house.push(vv.id);
                        });
                    }
                });
                data.factoryRange = factory;
                data.houseRange = house;
                return data;
            });
        },
        password: function () {
            ea.listen();
        }
    };
    return Controller;
});