define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.scale/index',
        add_url: 'system.scale/add',
        edit_url: 'system.scale/edit',
        delete_url: 'system.scale/delete',
        export_url: 'system.scale/export',
        modify_url: 'system.scale/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'xh', title: '排序'},
                    {field: 'name', title: '规模名称'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});