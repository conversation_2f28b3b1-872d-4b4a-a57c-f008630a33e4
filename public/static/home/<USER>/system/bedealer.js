define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.bedealer/index',
        add_url: 'system.bedealer/add',
        edit_url: 'system.bedealer/edit',
        delete_url: 'system.bedealer/delete',
        export_url: 'system.bedealer/export',
        modify_url: 'system.bedealer/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {field: 'fname', title: '姓名'},
                    {field: 'fcompany', title: '经销商名称'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});