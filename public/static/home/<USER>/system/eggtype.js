define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.eggtype/index',
        add_url: 'system.eggtype/add',
        edit_url: 'system.eggtype/edit',
        delete_url: 'system.eggtype/delete',
        export_url: 'system.eggtype/export',
        modify_url: 'system.eggtype/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'typename', title: '鸡蛋品名'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});