define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.pflist/index',
        add_url: 'feed.pflist/add',
        edit_url: 'feed.pflist/edit',
        delete_url: 'feed.pflist/delete',
        export_url: 'feed.pflist/export',
        modify_url: 'feed.pflist/modify',
        view_url: 'feed.pflist/view',
        made_url: 'feed.pflist/made',
    };

    var Controller = {

        index: function () {
            zn.createFactoryTab('factory_id');
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory_name', title: '厂区',search: false},
                    {field: 'pf_no', title: '配方编码',search: false},
                    {field: 'pfname', title: '配方名称'},
                    {field: 'weight', title: '配方重量(Kg)',search: false},
                    {field: 'status', title: '状态', width: 85, search: 'select', selectList: {0: '不可用', 1: '可用'}, templet: ea.table.switch},
                    {field: 'stage', title: '适用生长期',selectList:{1:'育雏期',2:'育成期',3:'产蛋期',4:'产蛋高峰期'}},
                    {
                        width: 250, title: '操作', templet: ea.table.tool,
                        operat: [
                            [{
                                text: '查看',
                                url: init.view_url,
                                method: 'open',
                                auth: 'view',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="false"',
                            }, {
                                text: '配制配方',
                                url: init.made_url,
                                method: 'open',
                                auth: 'made',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }], 'edit', 'delete'
                        ]
                    },
                ]],
            });
            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
        view:function(){

            ea.listen();
        },
        made:function(){
            $('#select_row').on('click',function (){
                layer.alert('点几')
            })
            ea.listen();
        }
    };
    return Controller;
});