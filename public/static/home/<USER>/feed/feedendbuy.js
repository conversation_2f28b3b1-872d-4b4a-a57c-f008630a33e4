define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.feedendbuy/index',
        add_url: 'feed.feedendbuy/add',
        edit_url: 'feed.feedendbuy/edit',
        delete_url: 'feed.feedendbuy/delete',
        export_url: 'feed.feedendbuy/export',
        modify_url: 'feed.feedendbuy/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'feedendList.feedname', title: '饲料名称'},
                    {field: 'cdate', title: '购买日期',search: 'range'},
                    {field: 'price', title: '单价',search: false},
                    {field: 'num', title: '数量（Kg）',search: false},
                    {field: 'amount', title: '总价', templet: ea.table.price, search: false},
                    // {field: 'wareHouse.name', title: '仓库',search: false},
                    {field: 'supperList.comname', title: '供应商',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            zn.resultPrice();//计算单价
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            zn.resultPrice();//计算单价
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
    };
    return Controller;
});