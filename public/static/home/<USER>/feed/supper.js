define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.supper/index',
        add_url: 'feed.supper/add',
        edit_url: 'feed.supper/edit',
        delete_url: 'feed.supper/delete',
        export_url: 'feed.supper/export',
        modify_url: 'feed.supper/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'comname', title: '公司名称'},
                    {field: 'contacts', title: '联系人'},
                    {field: 'phone', title: '联系电话'},
                    {field: 'area', title: '地区',search:false},
                    {field: 'address', title: '详细地址',search:false},
                    {field: 'range', title: '经营范围',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});