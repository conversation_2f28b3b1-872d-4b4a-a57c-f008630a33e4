define(["jquery", "easy-admin","znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.stock/index',
        add_url: 'feed.stock/add',
        edit_url: 'feed.stock/edit',
        delete_url: 'feed.stock/delete',
        export_url: 'feed.stock/export',
        modify_url: 'feed.stock/modify',
        view_url: 'feed.stock/view',
    };

    var Controller = {

        index: function () {
            zn.createFactoryTab('feed_stock.factory_id');
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'code', title: '生产单号'},
                    {field: 'feedpfList.pfname', title: '饲料名称'},
                    {field: 'real_num', title: '数量（Kg）',search: false},
                    {field: 'raw_cost', title: '原料成本(￥)',search: false},
                    {field: 'manag_cost', title: '管理成本(￥)',search: false},
                    {field: 'prod_cost', title: '吨平成本(￥)',search: false},
                    {field: 'price', title: '单价(每Kg)',search: false,templet: ea.table.price},
                    {field: 'cdate', title: '生产日期',search: 'range'},
                    {width: 250, title: '操作', templet: ea.table.tool,
                    operat:[[
                        {
                            text: '打印生产单',
                            url: init.view_url,
                            method: 'open',
                            auth: 'view',
                            class: 'layui-btn layui-btn-xs layui-btn-normal',
                            extend: 'data-full="true"',
                        },
                        {
                            text: '编辑',
                            url: init.edit_url,
                            method: 'open',
                            auth: 'edit',
                            class: 'layui-btn layui-btn-xs',
                            extend: 'data-full="true"',
                        }
                    ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;

            $('#rawCost').on('click',function(d){
                this.innerHTML='重新计算原料成本';
               getRaw('add');

            });

            form.on('submit(savePrint)', function (data) {
                layer.msg(JSON.stringify(data.field));
                console.log(data);
                $.ajax({
                    url:'savePrint',
                    data:data,
                    type:'post',
                    dataType: 'json',
                    success:res=>{
                        console.log(res);
                    }
                })

            })
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            },function(data){
                var id = data.data;
                window.location.href='view?id='+id;
            });
        },
        edit: function () {
            var form = layui.form;
            $('#rawCost').on('click',function(d){
                this.innerHTML='重新计算原料成本';
                getRaw('edit');

            });
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        view:function(){
            ea.listen();
        }
    };
    function getRaw(type){
        let pf_id=$('#pf_id').val() || $('#pfid').val();
        let num = $('#num').val();
        let cdate = $('#cdate').val();
        let id = 0;
        let factory_id = $('#factory_id').val();
        if(factory_id===''){
            layer.msg('请选择一个厂区！',{icon:2});
            return false;
        }
        if($('#id').length>0) {
            id = $('#id').val();
        }
        if(pf_id=='' || num=='' || cdate==''){
            layer.msg('数据不完整,无配方或者数量、日期数据！');
            return false;
        }

        $.ajax({
            type:'post',
            url:'../Common/getRawPf',
            dataType:'json',
            data:{
                pf_id:pf_id,
                num:num,
                cdate:cdate,
                type:type,
                id:id,
                factory_id:factory_id
            },
            success:res=>{
                console.log(res);
                var row='<thead><tr><th>原料名称</th><th>所需重量</th><th>单价</th><th>核算成本</th><th>库存情况</th></tr></thead><tbody>';
                var costTotal=0;
                for(i in res){
                    row+=`<tr>
                    <td>${res[i].raw.rawname}<input type="hidden" name="raw_id[]" value="${res[i].raw.id}"> </td>
                    <td>${res[i].pfNum}Kg <input type="hidden" name="raw_num[]" value="${res[i].pfNum}"></td>
                    <td>￥ ${res[i].price}<input type="hidden" name="price[]" value="${res[i].price}"></td>
                    <td>￥ ${res[i].raw_cost} <input type="hidden" name="cost[]" value="${res[i].raw_cost}"></td>
                    <td>${res[i].note} <input type="hidden" name="raw_notes[]" value="${res[i].note}">
                    <input type="hidden" name="stock_num[]" value="${res[i].stockNum}">
                    <input type="hidden" name="stock_amount[]" value="${res[i].stockAmount}">
                    </td>
                    
                </tr>`
                    costTotal += parseFloat(res[i].raw_cost);
                }
                row+='</tbody>';
                console.log(row);
                $('#costTable tr').remove();
                $('#costTable').append(row);
                $('#costTotal').html('￥'+costTotal.toFixed(2));
            }

        })
    }
    return Controller;
});