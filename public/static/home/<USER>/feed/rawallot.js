define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.rawallot/index',
        add_url: 'feed.rawallot/add',
        edit_url: 'feed.rawallot/edit',
        delete_url: 'feed.rawallot/delete',
        export_url: 'feed.rawallot/export',
        modify_url: 'feed.rawallot/modify',
        print_url: 'feed.rawallot/print',
    };

    var Controller = {

        index: function () {
            zn.createFactoryTab('raw_allot.factory_out_id')
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'rawList.rawname', title: '原料名称'},
                    {field: 'num', title: '数量（Kg）',search:false},
                    {field: 'cdate', title: '调拨日期',search:'range'},
                    {field: 'amount', title: '总金额',templet: ea.table.price,search:false},
                    {field: 'price', title: '单价',templet: ea.table.price,search:false},
                    {field: 'factoryOut.factoryname', title: '调出单位',search:false},
                    {field: 'factoryIn.factoryname', title: '调入单位',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool, operat: [[
                            {
                                text: '打印入库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
        edit: function () {
            ea.listen(function(data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
    };
    return Controller;
});