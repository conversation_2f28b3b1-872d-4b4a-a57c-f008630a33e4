define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.raw/index',
        add_url: 'feed.raw/add',
        edit_url: 'feed.raw/edit',
        delete_url: 'feed.raw/delete',
        export_url: 'feed.raw/export',
        modify_url: 'feed.raw/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sort', width: 80, title: '排序', edit: 'text', search: false},
                    {field: 'rawcode', title: '原料编码', search: false},
                    {field: 'rawname', title: '原料名称'},
                    {field: 'remark', title: '备注', search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});