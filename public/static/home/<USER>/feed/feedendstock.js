define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.feedendstock/index',
        add_url: 'feed.feedendstock/add',
        edit_url: 'feed.feedendstock/edit',
        delete_url: 'feed.feedendstock/delete',
        export_url: 'feed.feedendstock/export',
        modify_url: 'feed.feedendstock/modify',
        view_url: 'feed.feedendstock/view',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {field: 'feedname', title: '饲料名称'},
                    {field: 'brand', title: '品牌',search:false},
                    {field: 'buyNum', title: '购置总数',search:false},
                    {field: 'receiveNum', title: '领取总数',search:false},
                    {field: 'stockNum', title: '库存数量',templet:function (d){
                            if(d.stockNum<d.stock_warn){
                                return "<span style='color:red'>"+d.stockNum+" ↓</span>";
                            }else{
                                return d.stockNum;
                            }
                        },search: false},
                    {field: 'stock_warn', title: '警戒库存(Kg)',search: false},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };

    return Controller;
});