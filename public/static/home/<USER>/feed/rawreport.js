define(["jquery", "easy-admin","miniTab"], function ($, ea, miniTab) {

    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;


            ea.listen();
        },
        dayreport: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;


            ea.listen();
        },
        monthreport: function () {
            miniTab.listen();
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;

            $('#saveMonth').on('click', function() {
                // 点击按钮弹出确认框
                layer.confirm('请仔细查看数据是否正确？', {icon: 3, title:'提示'}, function(index){
                    // 确认执行的回调
                    // 例如：发起Ajax请求，执行操作
                    // 操作完成后，可以关闭弹层
                    saveMonth();
                    layer.close(index);
                    // setTimeout(function(){
                    //
                    // },2000);

                    // 或者如果需要关闭当前页面
                    // window.parent.location.reload(); // 如果是iframe页面
                    // window.location.reload(); // 直接当前页面刷新
                });

            });


            ea.listen();
        },

    };
    function saveMonth(){
        console.log(dataFromPHP); // 输出: value
        var data = JSON.stringify(dataFromPHP);
        $.ajax({
            url:'../Common/rawMonthSave',
            type:'post',
            data:{data:data},
            dataType:'json',
            success:function(res){
                console.log(res);
                if(res){
                    layer.alert('结算成功，不可修改！');
                    $('#saveMonth').prop('disabled', true);
                    $('#saveMonth').addClass("layui-btn-disabled");
                    $('#tongji').html('查看');
                }else{
                    layer.alert('提交失败！')
                }
            },
            error:function (res){
                layer.alert('保存失败');
            }
        })
    }
    return Controller;
});