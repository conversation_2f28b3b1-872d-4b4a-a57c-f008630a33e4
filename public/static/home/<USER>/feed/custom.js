define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.custom/index',
        add_url: 'feed.custom/add',
        edit_url: 'feed.custom/edit',
        delete_url: 'feed.custom/delete',
        export_url: 'feed.custom/export',
        modify_url: 'feed.custom/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'unitname', title: '公司名称'},
                    {field: 'type', title: '客户类别',selectList:{0:' ','1':'孵化场','2':'青年鸡场','3':'商品蛋鸡场','4':'综合鸡场'},search:false},
                    {field: 'scalename', title: '规模',search:false},
                    {field: 'level', title: '级别',selectList:{0:' ','1':'大客户','2':'中型客户','3':'小客户'},search:false},
                    {field: 'name', title: '联系人'},
                    {field: 'phone', title: '联系电话'},
                    {field: 'area', title: '地区',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '生产提醒',
                                url: init.record_url,
                                method: 'open',
                                auth: 'record',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            }
                        ],'edit','delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});