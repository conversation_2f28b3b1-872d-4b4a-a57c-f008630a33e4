define(["jquery", "easy-admin","znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.feedreceive/index',
        add_url: 'feed.feedreceive/add',
        edit_url: 'feed.feedreceive/edit',
        delete_url: 'feed.feedreceive/delete',
        export_url: 'feed.feedreceive/export',
        modify_url: 'feed.feedreceive/modify',
        print_url: 'feed.feedreceive/print',
    };

    var Controller = {
        index: function () {
            zn.createFactoryTab('feedend_buy_main.factory_id');
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '出库单号'},
                    {field: 'feedname', title: '饲料名称'},
                    {field: 'num', title: '数量（Kg）', search: false},
                    {field: 'cdate', title: '领取时间', search: 'range'},
                    {field: 'factoryIn.factoryname', title: '领用单位', search: false},
                    {field: 'factory.factoryname', title: '领出单位'},
                    {
                        width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '打印出库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            //获取该厂区的仓库列表
            form.on('select(factory_id)',function(data){
                zn.getWarehouse(data.value);
            })
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            var form = layui.form;
            //获取该厂区的仓库列表
            form.on('select(factory_id)',function(data){
                zn.getWarehouse(data.value);
            })
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
    };
    return Controller;
});