define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.sell/index',
        add_url: 'feed.sell/add',
        edit_url: 'feed.sell/edit',
        delete_url: 'feed.sell/delete',
        export_url: 'feed.sell/export',
        modify_url: 'feed.sell/modify',
        sellprint_url: 'feed.sell/sellprint',
    };
    var form=layui.form;
    var index = $('#index').val();
    var Controller = {
        index: function () {
            zn.createFactoryTab('feed_sell_main.factory_id');
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '销售',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sale_no', title: '出库单号'},
                    {field: 'cdate', title: '销售日期',search:'range'},
                    {field: 'munit', title: '单位',search:false,selectList:{'0':'公斤','1':'吨'}},
                    {field: 'amount', title: '金额',templet:ea.table.price,search:false},
                    {field: 'factory.factoryname', title: '售出单位',search:false},
                    {field: 'userCustom.unitname', title: '客户',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印销售单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
    };
    $('#add').on('click',function (){
        index++;
        var selectid = 'pf_id'+index;
        var div_id = 'div'+index;
        var num_id = 'num'+index;
        var amount_id = 'amount'+index;
        var price_id = 'price'+index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id=""><select name="pf_id[]" id="'+selectid+'" lay-verify="required"></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="'+num_id+'" placeholder="请输入数量"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="'+price_id+'" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="'+amount_id+'" value="" placeholder="请输入金额"  onblur="setTotal('+index+')"/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect(selectid);
        form.render('select');
    })
    function createSelect(selectid) {
        var source = document.getElementById('pf_id1');
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }
    return Controller;
});