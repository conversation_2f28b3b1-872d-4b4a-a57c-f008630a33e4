define(["jquery", "easy-admin"], function ($, ea) {

    var Controller = {
        index: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            ea.listen();
        },
        dayreport: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            ea.listen();
        },
        monthreport: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            $('#receive1').on('click',function (){
                console.log('aaaaaaaaaaaa');
                var cdate = $('#month').val();
                if(cdate===''){
                    layer.alert('请选择月份！');
                    return false;
                }else{
                    layer.open({
                        type: 2,
                        title: '领料统计月报('+cdate+')',
                        content: 'receive?cdate=' + encodeURIComponent(cdate),
                        area: ['800px', '95%'], // 弹窗的尺寸
                        success: function(layero, index){
                            // 成功打开弹窗后的回调函数
                        }
                    });
                }
            })

            ea.listen();
        },
        feeddetails:function(){
            ea.listen();
        }
    };
    return Controller;
});