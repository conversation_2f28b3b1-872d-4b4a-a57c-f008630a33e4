define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.rawsell/index',
        add_url: 'feed.rawsell/add',
        edit_url: 'feed.rawsell/edit',
        delete_url: 'feed.rawsell/delete',
        export_url: 'feed.rawsell/export',
        modify_url: 'feed.rawsell/modify',
        sellprint_url: 'feed.rawsell/sellprint',
        search_url:'feed.rawsell/search',
    };
    var form=layui.form;
    var index = Number($('#index').val());
    var Controller = {
        index: function () {
            zn.createFactoryTab('raw_sell_main.factory_id');
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '销售',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'cdate', title: '销售日期',search:'range'},
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'amount', title: '结算金额',templet: ea.table.price,search:false},
                    {field: 'factory.factoryname', title: '销售单位'},
                    {field: 'userCustom.name', title: '客户'},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印销售单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        search:function(){
            var cols=[[
                {field: 'sale_no', title: '销售单号',align:"center"},
                {field: 'rawname', title: '原料名称',align:"center"},
                {field: 'cdate', title: '销售日期', align:"center"},
                {field: 'num', title: '数量', align:"center"},
                {field: 'price', title: '单价', align:"center"},
                {field: 'amount', title: '金额', align:"center"},
                {field: 'userCustomName', title: '客户', align:"center"},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },

    };


    $('#add').on('click',function (){
        index++;
        var selectid = 'raw'+index;
        var div_id = 'div'+index;
        var num_id = 'num'+index;
        var amount_id = 'amount'+index;
        var price_id = 'price'+index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id=""><select name="raw_id[]" id="'+selectid+'" lay-verify="required"  lay-search></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="'+num_id+'" placeholder="请输入数量"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="'+price_id+'" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="'+amount_id+'" value="" placeholder="请输入金额"  onblur="setTotal('+index+')"/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect(selectid);
        form.render('select');
    })
    function createSelect(selectid) {
        var source = document.getElementById('raw1');
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }

    return Controller;
});