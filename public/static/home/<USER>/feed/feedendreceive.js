define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.feedendreceive/index',
        add_url: 'feed.feedendreceive/add',
        edit_url: 'feed.feedendreceive/edit',
        delete_url: 'feed.feedendreceive/delete',
        export_url: 'feed.feedendreceive/export',
        modify_url: 'feed.feedendreceive/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'feedendList.feedname', title: '饲料名称'},
                    {field: 'num', title: '数量（Kg）',search: false},
                    {field: 'cdate', title: '领取时间',search: 'range'},
                    {field: 'factory.factoryname', title: '领用厂',search: false},
                    {field: 'wareHouse.name', title: '领用仓库'},
                    {width: 250,title: '操作',templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
    };
    return Controller;
});