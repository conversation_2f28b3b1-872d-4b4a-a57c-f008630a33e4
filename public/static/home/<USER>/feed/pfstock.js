define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.pfstock/index',
        add_url: 'feed.pfstock/add',
        edit_url: 'feed.pfstock/edit',
        delete_url: 'feed.pfstock/delete',
        export_url: 'feed.pfstock/export',
        modify_url: 'feed.pfstock/modify',
    };
    var init2 = {
        table_elem: '#currentTableReceive',
        table_render_id: 'currentTableRenderIdReceive',
        index_url: 'feed.pfstock/receivelist',
    };
    var Controller = {
        index: function () {
            zn.createFactoryTab('factory_id');
            ea.table.render({
                init: init,
                toolbar:['refresh'],
                cols: [[
                    {field: 'pfname', title: '饲料名称'},
                    {field: 'buyNum', title: '生产数量(Kg)',search:false},
                    {width: 200, title: '领取数量(Kg)',templet:function (d){
                        return d.receiveNum;
                        }},
                    {field: 'sellNum', title: '销售数量(Kg)',search:false},
                    {field: 'stockNum', title: '库存数量(Kg)',templet:function (d){
                        if(d.stockNum<=d.stock_warn){
                            return "<span style='color:red'>"+d.stockNum+" ↓</span>";
                        }else{
                            return d.stockNum;
                        }
                        },search:false},
                    {field: 'stock_warn', title: '警戒库存',search:false},
                ]],
            });

            ea.listen();
        },
        receivelist: function () {
            var id=getQueryVariable("id");
            ea.table.render({
                init: init2
                ,url: 'receivelist?id='+id // 后端数据接口URL地址
                ,page: true // 开启分页
                ,limit: 10 // 每页显示的条数
                ,limits: [10,20,30] // 每页条数选项
                ,toolbar:['refresh'],
                cols: [[
                    {field: 'no', title: '单号'},
                    {field: 'factoryname', title: '领用厂'},
                    {field: 'num', title: '数量(Kg)',search:false},
                    {field: 'cdate', title: '日期',search:false},

                ]],
            });

            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});
//接收页面传值的方法
function getQueryVariable(variable){
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
        var pair = vars[i].split("=");
        if(pair[0] == variable){return pair[1];}
    }
    return(false);
}