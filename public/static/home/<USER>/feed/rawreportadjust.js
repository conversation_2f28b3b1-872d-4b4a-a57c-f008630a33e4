define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'feed.rawreportadjust/index',
        add_url: 'feed.rawreportadjust/add',
        edit_url: 'feed.rawreportadjust/edit',
        delete_url: 'feed.rawreportadjust/delete',
        export_url: 'feed.rawreportadjust/export',
        modify_url: 'feed.rawreportadjust/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryName', title: '厂区'},
                    {field: 'rawName', title: '原料'},
                    {field: 'cdate', title: '调整日期'},
                    {field: 'num', title: '调整数量'},
                    {field: 'amount', title: '调整金额',templet:ea.table.price},
                    {field: 'note', title: '说明'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
        edit: function () {
            ea.listen(function(data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
    };
    return Controller;
});