define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'envmon.envinfo/index',
        add_url: 'envmon.envinfo/add',
        edit_url: 'envmon.envinfo/edit',
        delete_url: 'envmon.envinfo/delete',
        export_url: 'envmon.envinfo/export',
        modify_url: 'envmon.envinfo/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'housename', title: '鸡舍名称'},
                    {field: 'wd', title: '温度'},
                    {field: 'sd', title: '湿度'},
                    {field: 'fs', title: '风速'},
                    {field: 'co2', title: 'co2'},
                    {field: 'wdavg', title: '平均温度'},
                    {field: 'sdavg', title: '平均湿度'},
                    {field: 'fsavg', title: '平均风速'},
                    {field: 'co2avg', title: '平均co2'},
                    // {field: 'fj_status', title: '风机状态'},
                    // {field: 'fc_tatus', title: '风窗状态'},
                    // {field: 'dfb_status', title: '导风板状态'},
                    // {field: 'sl_status', title: '湿帘状态'},
                    // {field: 'hot_status', title: '加热器状态'},
                    {field: 'name', title: '采集人'},
                    {field: 'cdate', title: '采集时间'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
    };
    return Controller;
});