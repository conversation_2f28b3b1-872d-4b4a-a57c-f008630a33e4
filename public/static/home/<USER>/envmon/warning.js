define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'envmon.warning/index',
        add_url: 'envmon.warning/add',
        edit_url: 'envmon.warning/edit',
        delete_url: 'envmon.warning/delete',
        export_url: 'envmon.warning/export',
        modify_url: 'envmon.warning/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar:['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'title', title: '标题',search: false},
                    {field: 'housename', title: '鸡舍名称',search: false},
                    {field: 'content', title: '内容'},
                    {field: 'createTime', title: '时间',search:'range'},
                    {title: 'AI分析'},
                    //{field: 'mode', title: '报警方式', selectList: {0: '全部', 1: '短信', 2: '电话',3:'推送',4:'短信+电话',5:'短信+电话+推送'}},

                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});