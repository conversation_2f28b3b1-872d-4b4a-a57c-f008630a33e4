define(["jquery", "easy-admin", "miniTab"], function ($, ea, miniTab) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'envmon.realtimedata1/index',
        add_url: 'envmon.realtimedata1/add',
        edit_url: 'envmon.realtimedata1/edit',
        delete_url: 'envmon.realtimedata1/delete',
        export_url: 'envmon.realtimedata1/export',
        modify_url: 'envmon.realtimedata1/modify',
        info_url: 'envmon.realtimedata1/info',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh'],
                where:{farmid:getQueryVariable('farmid')},
                cols: [[
                    {field: 'factoryname', title: '厂区',search:false},
                    {field: 'housename', title: '鸡舍',search:false},
                    {field: 'sn', title: 'SN码',search:false},
                    {field: 'onlineState', title: '状态',selectList: {0: '<span class="layui-badge">离线</span>', 1: '<span class="layui-badge layui-bg-green">在线</span>'},with:80,search:false},
                    {field: 'list_time', title: '最新数据时间',search:false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat: [
                            [{
                                text: '详情',
                                url: init.info_url,
                                method: 'open',
                                auth: 'info',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                                field:'sn',
                            }]
                        ]
                    },
                ]],
            });

            ea.listen();
        },
        add:function () {
            ea.listen();
        },
        edit:function () {
            ea.listen();
        },
        info:function () {
            //十五秒刷新一次
            window.reloadView = function () {
                window.location.reload();
            };
            ea.listen();
        }
    };

    return Controller;
})

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
        var pair = vars[i].split("=");
        if(pair[0] == variable){return pair[1];}
    }
    return false;
}