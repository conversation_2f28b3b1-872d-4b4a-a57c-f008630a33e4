define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'envmon.datareport/index',
        add_url: 'envmon.datareport/add',
        edit_url: 'envmon.datareport/edit',
        delete_url: 'envmon.datareport/delete',
        export_url: 'envmon.datareport/export',
        modify_url: 'envmon.datareport/modify',
        info_url:'envmon.datareport/info',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'housename', title: '养殖舍'},
                    {field: 'zywddiff', title: '昼夜最大温差',search:false},
                    {field: 'timewddiff', title: '同时间最大温差',search:false},
                    {field: 'co2max', title: 'CO2最大值',search:false},
                    {field: 'kqzlmax', title: '空气质量最大值',search:false},
                    {field: 'cdate', title: '日期'},
                    {field: 'starttime', title: '开始计算时间',search:false},
                    {field: 'endtime', title: '结束计算时间',search:false},
                    {field: 'zywdmax', title: '昼夜温差最大植',search:false},
                    {field: 'zywdmin', title: '昼夜温差最小植',search:false},
                    {
                        width: 250,
                        title: '操作',
                        templet: ea.table.tool,
                        operat: [
                            [{
                                text: '详情',
                                url: init.info_url,
                                method: 'open',
                                auth: 'info',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }]
                        ]
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});
