define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'sell.chicken/index',
        add_url: 'sell.chicken/add',
        edit_url: 'sell.chicken/edit',
        delete_url: 'sell.chicken/delete',
        export_url: 'sell.chicken/export',
        modify_url: 'sell.chicken/modify',
        sellprint_url: 'sell.chicken/sellprint',
    };
    var index = Number($('#index').val());
    var form=layui.form;
    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '销售',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete', 'export'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'cdate', title: '日期',search:'range'},
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'num', title: '数量总计(只)',search:false},
                    {field: 'amount', title: '结算金额',templet: ea.table.price,search:false},
                    {field: 'worker.name', title: '负责人',search: false},
                    {field: 'custom.name', title: '客户'},
                    {width: 250, title: '操作', templet: ea.table.tool, operat:[[
                            {
                                text: '打印销售单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        edit: function () {
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
    };


    $('#add').on('click',function (){
        index++;
        console.log(index);
        var house_id = 'houseid'+index;
        var div_id = 'div'+index;
        var num_id = 'num'+index;
        var amount_id = 'amount'+index;
        var price_id = 'price'+index;
        var batch_name_id = 'batch_name'+index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id="house"><select name="prod_id[]" lay-verify="required" id="'+house_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="batch_name[]" id="'+batch_name_id+'" value="" placeholder="批次" /></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="'+num_id+'" placeholder="请输入数量"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="'+price_id+'" placeholder="请输入价格"  onblur="setTotal('+index+')"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="'+amount_id+'" value="" placeholder="请输入金额"  onblur="setTotal('+index+')" /></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect('houseid1',house_id);
        form.render('select');
    })

    function createSelect(sourceid,selectid) {
        var source = document.getElementById(sourceid);
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }
    return Controller;
});