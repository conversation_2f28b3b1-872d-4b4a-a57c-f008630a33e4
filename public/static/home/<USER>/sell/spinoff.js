define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'sell.spinoff/index',
        add_url: 'sell.spinoff/add',
        edit_url: 'sell.spinoff/edit',
        delete_url: 'sell.spinoff/delete',
        export_url: 'sell.spinoff/export',
        modify_url: 'sell.spinoff/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'houseprod.house.housename', title: '鸡舍', search: false, hide: true},
                    {field: 'spin_name', title: '名称', search: false},
                    {field: 'cdate', title: '销售日期', search: 'range'},
                    {field: 'num', title: '数量', search: false},
                    {field: 'inc', title: '单位', selectList: {'0': 'Kg', '1': '只', '2': '吨'}, search: false},
                    {field: 'price', title: '单价', templet: ea.table.price, search: false},
                    {field: 'amount', title: '金额', templet: ea.table.price, search: false},
                    {field: 'givenum', title: '赠送数量', search: false},
                    {field: 'custom.unitname', title: '收购单位', search: false},
                    {field: 'remark', title: '备注', search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            zn.resultPrice();
            var form = layui.form;
            form.on('select(factory_id)', function (data) {
                zn.getHouseProd(data.value);
            })

            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });

        },
        edit: function () {
            zn.resultPrice();
            var form = layui.form;
            form.on('select(factory_id)', function (data) {
                zn.getHouseProd(data.value);
            })
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
    };
    $('#showHouse').on('click', function () {
        let div = document.getElementById('factory');
        var style = window.getComputedStyle(div);
        if (style.display === "none") {
            div.style.display = "block"
            $('#icon').removeClass('layui-icon-add-circle').addClass('layui-icon-reduce-circle');
        } else {
            div.style.display = "none"
            $('#icon').removeClass('layui-icon-reduce-circle').addClass('layui-icon-add-circle');
        }

    })
    return Controller;
});