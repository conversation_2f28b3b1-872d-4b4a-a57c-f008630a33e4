define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'device.myreg/index',
        add_url: 'device.myreg/add',
        edit_url: 'device.myreg/edit',
        delete_url: 'device.myreg/delete',
        export_url: 'device.myreg/export',
        modify_url: 'device.myreg/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: "checkbox"},
                    //{field: 'modelname', minWidth: 80, title: '型号',search: false},
                    {field: 'sn', minWidth: 80, title: 'SN码'},
                    {field: 'house.housename', minWidth: 80, title: '鸡舍',search: false},
                    {field: 'cdate', minWidth: 80, title: '购买日期', search: 'range'},
                    {field: 'fcompany', minWidth: 80, title: '购买渠道', search: false},
                    {field: 'remark', minWidth: 80, title: '备注信息',search: false},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
        
    };
    return Controller;
});