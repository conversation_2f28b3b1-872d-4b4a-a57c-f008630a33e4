define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'device.stock/index',
        add_url: 'device.stock/add',
        edit_url: 'device.stock/edit',
        delete_url: 'device.stock/delete',
        export_url: 'device.stock/export',
        modify_url: 'device.stock/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: "checkbox"},
                    // {field: 'id', width: 80, title: 'ID',search: false},
                    {field: 'modelname', minWidth: 80, title: '型号',search: false},
                    {field: 'sn', minWidth: 80, title: 'SN码'},
                    {field: 'cdate', minWidth: 80, title: '购买日期', search: 'range'},
                    {field: 'prodList.proddate', minWidth: 80, title: '出厂日期',search: false},
                    {field: 'remark', minWidth: 80, title: '备注信息',search: false},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
        
    };
    return Controller;
});