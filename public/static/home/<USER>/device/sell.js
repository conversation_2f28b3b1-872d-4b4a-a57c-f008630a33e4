define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'device.sell/index',
        add_url: 'device.sell/add',
        edit_url: 'device.sell/edit',
        delete_url: 'device.sell/delete',
        export_url: 'device.sell/export',
        modify_url: 'device.sell/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: "checkbox"},
                    // {field: 'id', width: 80, title: 'ID',search: false},
                    {field: 'modelname', minWidth: 80, title: '型号',search: false},
                    {field: 'sn', minWidth: 80, title: 'SN码'},
                    {field: 'name', minWidth: 80, title: '购买方',search: false},
                    {field: 'housename', minWidth: 80, title: '鸡舍',search: false},
                    {field: 'cdate', minWidth: 80, title: '售出日期', search: 'range'},
                    // {
                    //     width: 250, title: '操作', templet: ea.table.tool,
                    //     operat: ['edit',
                    //         [{
                    //             text: '撤消',
                    //             url: init.delete_url,
                    //             method: 'request',
                    //             auth: 'delete',
                    //             class: 'layui-btn layui-btn-xs layui-btn-danger',
                    //             extend: 'data-full="false"',
                    //         }]
                    //     ]
                    // }
                ]],
            });

            ea.listen();
        },
        add: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            form.on('select(myfarm)', function (data) {
                var farmid = data.value;
                $.ajax({
                    type: 'POST',
                    url: 'getHouseid',
                    data: {"farmid": farmid},
                    dataType: 'json',
                    success: function (data) {
                        $("#houseid").html("");
                        $.each(data, function (key, val) {
                            var option1 = $("<option>").val(val.id).text(val.housename);
                            //通过LayUI.jQuery添加列表项
                            $("#houseid").append(option1);
                            form.render('select');
                        });
                        $("#houseid").get(0).selectedIndex = 0;
                    }
                });
            });
            form.on('select(myuser)', function (data) {
                var usertype = data.value;
                if (usertype == 1) {
                    $("#farm").css({'display': 'none'});
                    $("#dealer").css({'display': 'block'});
                } else {
                    $("#farm").css({'display': 'block'});
                    $("#dealer").css({'display': 'none'});
                }

            });
            ea.listen();
        },
        edit: function () {
            var layer = layui.layer
                , $ = layui.jquery
                , form = layui.form;
            form.on('select(myfarm)', function (data) {
                var farmid = data.value;
                $.ajax({
                    type: 'POST',
                    url: 'getHouseid',
                    data: {"farmid": farmid},
                    dataType: 'json',
                    success: function (data) {
                        $("#houseid").html("");
                        $.each(data, function (key, val) {
                            var option1 = $("<option>").val(val.id).text(val.housename);
                            //通过LayUI.jQuery添加列表项
                            $("#houseid").append(option1);
                            form.render('select');
                        });
                        $("#houseid").get(0).selectedIndex = 0;
                    }
                });
            });
            form.on('select(myuser)', function (data) {
                var usertype = data.value;
                if (usertype == 1) {
                    $("#farm").css({'display': 'none'});
                    $("#dealer").css({'display': 'block'});
                } else {
                    $("#farm").css({'display': 'block'});
                    $("#dealer").css({'display': 'none'});
                }

            });
            ea.listen();
        },

    };
    return Controller;
});