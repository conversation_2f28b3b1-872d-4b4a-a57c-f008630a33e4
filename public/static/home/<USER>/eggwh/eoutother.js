define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'eggwh.eoutother/index',
        add_url: 'eggwh.eoutother/add',
        edit_url: 'eggwh.eoutother/edit',
        delete_url: 'eggwh.eoutother/delete',
        export_url: 'eggwh.eoutother/export',
        modify_url: 'eggwh.eoutother/modify',
        sellprint_url: 'eggwh.eoutother/sellprint',
        search_url: 'eggwh.eoutother/search',
    };
    var index = Number($('#index').val());
    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'housename', title: '来源鸡舍'},
                    {field: 'cdate', title: '日期'},
                    // {field: 'num', title: '数量'},
                    // {field: 'weight', title: '重量(斤)},
                    {field: 'purpose', title: '用途'},
                    {field: 'notes', title: '说明'},
                    {
                        width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '打印出库单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra: 'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ], 'delete']
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;


            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
        edit: function () {
            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            })
        },
        sellprint: function () {
            ea.listen();
        },
        search:function(){
            var form = layui.form;
            //厂区鸡舍联动
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            var cols=[[
                {field: 'sale_no', title: '单号',align:"center"},
                {field: 'housename', title: '鸡舍',align:"center"},
                {field: 'eggtype', title: '品种',align:"center"},
                {field: 'unitname', title: '类型',align:"center"},
                {field: 'classname', title: '类型',align:"center"},
                {field: 'cdate', title: '日期',align:"center"},
                {field: 'num', title: '数量',align:"center"},
                {field: 'weight', title: '重量(斤)',align:"center"},
                {field: 'purpose', title: '用途',align:"center"},
                {field: 'notes', title: '说明',align:"center"},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },
    };
    var form=layui.form;
    $('#add').on('click',function (){
        var factory_id = $('#factory_id').val();
        if(factory_id==''){
            layer.alert('请选择厂区');
            return false;
        }
        index++;
        console.log(index);
        var house_id = 'houseid'+index;
        var class_id = 'class'+index;
        var unit_id = 'unit'+index;
        var div_id = 'div'+index;
        var num_id = 'snum'+index;
        var weight_id = 'sweight'+index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id="house"><select name="prod_id[]" lay-verify="required" id="'+house_id+'"></select></td>' +
            '<td><select name="unit_id[]" lay-verify="required" id="'+unit_id+'"></select></td>' +
            '<td><select name="class_id[]" lay-verify="required" id="'+class_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="snum[]" id="'+num_id+'" placeholder="数量"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><input type="text" class="layui-input" name="sweight[]" id="'+weight_id+'" placeholder="重量，斤"  onblur="setTotal('+index+')" value=""/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        zn.createSelect('houseid1',house_id);
        zn.createSelect('class1',class_id);
        zn.createSelect('unit1',unit_id);
        form.render('select');
    })


    return Controller;
});