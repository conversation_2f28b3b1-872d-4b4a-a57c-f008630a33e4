define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'eggwh.eunit/index',
        add_url: 'eggwh.eunit/add',
        edit_url: 'eggwh.eunit/edit',
        delete_url: 'eggwh.eunit/delete',
        export_url: 'eggwh.eunit/export',
        modify_url: 'eggwh.eunit/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'name', title: '规格'},
                    {field: 'num', title: '换算数量'},
                    {field: 'munit', title: '换算计量单位'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});