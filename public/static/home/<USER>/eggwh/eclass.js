define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'eggwh.eclass/index',
        add_url: 'eggwh.eclass/add',
        edit_url: 'eggwh.eclass/edit',
        delete_url: 'eggwh.eclass/delete',
        export_url: 'eggwh.eclass/export',
        modify_url: 'eggwh.eclass/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'name', title: '鸡蛋品相'},
                    {field: 'note', title: '说明'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});