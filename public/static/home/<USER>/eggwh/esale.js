define(["jquery", "easy-admin", "znegdoo"], function ($, ea,zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'eggwh.esale/index',
        add_url: 'eggwh.esale/add',
        edit_url: 'eggwh.esale/edit',
        delete_url: 'eggwh.esale/delete',
        export_url: 'eggwh.esale/export',
        modify_url: 'eggwh.esale/modify',
        sellprint_url: 'eggwh.esale/sellprint',
        search_url: 'eggwh.esale/search',
    };
    var index = Number($('#index').val());
    var wuliao_index = Number($('#wuliao_index').val());
    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                search:false,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sale_no', title: '销售单号'},
                    {field: 'factory.factoryname', title: '厂区'},
                    {field: 'cdate', title: '日期'},
                    {field: 'amount', title: '金额',search:false},
                    {field: 'custom.unitname', title: '收购单位',search:false,templet:function (d){
                        return d.custom_id===0?'':d.custom.unitname;
                        }},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印销售单',
                                url: init.sellprint_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],


        });

            ea.listen();
        },
        add: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });

        },
        edit: function () {

            ea.listen(function (data) {
                var date = new Date(ea.formatDate(data.cdate));
                data.cdate = date.getTime() / 1000;
                return data;
            });
        },
        search:function(){
            var cols=[[
                {field: 'sale_no', title: '销售单号',align:"center"},
                {field: 'housename', title: '鸡舍',align:"center"},
                {field: 'cdate', title: '销售日期',align:"center"},
                {field: 'unit_name', title: '规格',align:"center"},
                {field: 'class_name', title: '蛋型',align:"center"},
                {field: 'num', title: '数量',align:"center"},
                {field: 'weight', title: '重量(斤)',align:"center"},
                {field: 'price', title: '单价(元）',align:"center"},
                {field: 'amount', title: '金额(元)',align:"center"},
                {field: 'custom_name', title: '收购单位',align:"center"},
                {field: 'remark', title: '备注',align:"center"},
            ]];
            zn.doSearch(cols);
            ea.listen();
        },
        sellprint:function (){
            ea.listen();
        },
        houseview:function(){
            ea.listen();
        }
    };
    var form=layui.form;
    $('#add').on('click',function (){
        var factory_id = $('#factory_id').val();
        if(factory_id==''){
            layer.alert('请选择厂区');
            return false;
        }
        index++;
        wuliao_index++;
        console.log(index);
        var house_id = 'houseid'+index;
        var class_id = 'class'+index;
        var unit_id = 'unit'+index;
        var div_id = 'div'+index;
        var num_id = 'num'+index;
        var weight_id = 'weight'+index;
        var amount_id = 'amount'+index;
        var price_id = 'price'+index;
        var note_id = 'note'+index;
        //var price = $('#price1').val();
        var trdata = '<tr id="' + div_id + '">' +
            '<td><select name="prod_id[]" lay-verify="required" id="'+house_id+'"></select></td>' +
            '<td><select name="unit_id[]" lay-verify="required" id="'+unit_id+'"></select></td>' +
            '<td><select name="class_id[]" lay-verify="required" id="'+class_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="'+num_id+'" placeholder="数量"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="weight[]" id="'+weight_id+'" placeholder="斤"  value="" onblur="setTotal('+index+',1)"/></td>' +
            '<td><input type="text" class="layui-input" name="price[]" id="'+price_id+'" placeholder="单价"  onblur="setTotal('+index+',1)"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="amount_s[]" id="'+amount_id+'" value="" placeholder="请输入金额" onblur="setTotal('+index+',2)"  /></td>' +
            '<td><input type="text" class="layui-input" name="note[]" id="'+note_id+'" value="" placeholder="备注" /></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        zn.createSelect('houseid1',house_id);
        zn.createSelect('class1',class_id);
        zn.createSelect('unit1',unit_id);
        form.render('select');
    })
    $('#add_wuliao').on('click',function (){
        wuliao_index++;
        console.log(wuliao_index);
        var tr_id = 'tr'+wuliao_index;
        var sundries_id = 'sundries'+wuliao_index;
        var num_id = 's_num'+wuliao_index;
        var note_id = 's_note'+wuliao_index;
        var trRow = '<tr id="' + tr_id + '">' +
            '<td><select name="sundries_id[]" lay-verify="required" id="'+sundries_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="s_num[]" id="'+num_id+'" placeholder="数量"  value=""/></td>' +
            '<td><input type="text" class="layui-input" name="s_note[]" id="'+note_id+'" value="" placeholder="备注" /></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="wlRemove(' + wuliao_index + ');">移除</button></td>' +
            '</tr>';
        $('#wuliao_table').append(trRow);
        zn.createSelect('sundries',sundries_id);
        form.render('select');
    })



    return Controller;
});