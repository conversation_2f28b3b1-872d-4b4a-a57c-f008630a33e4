define(["jquery", "easy-admin", "znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'eggwh.estore/index',
        add_url: 'eggwh.estore/add',
        edit_url: 'eggwh.estore/edit',
        delete_url: 'eggwh.estore/delete',
        export_url: 'eggwh.estore/export',
        sctock_url: 'eggwh.estore/sctock',
        print_url: 'eggwh.estore/print',
        view_url: 'eggwh.estore/view',
        search_url: 'eggwh.estore/search',
    };
    var init_stock = {
        table_elem: '#currentTable_stock',
        table_render_id: 'currentTableRenderId_stock',
        index_url: 'eggwh.estore/stock',

    };
    var index = Number($('#index').val());
    var Controller = {
        index: function () {
            ea.table.render({
                init: init,
                search:true,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    },{
                        text: '明细查询',
                        url: init.search_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-bg-orange layui-btn-sm',
                        icon: 'fa fa-search ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'no', title: '入库单号'},
                    {field: 'factory.factoryname', title: '厂区'},
                    {field: 'cdate', title: '入库日期',search:'range'},
                    {field: 'worker.name', title: '库管员'},
                    {width: 250, title: '操作', templet: ea.table.tool,
                        operat:[[
                            {
                                text: '打印入库单',
                                url: init.print_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            },
                            {
                                text: '编辑',
                                extra:'name',
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }
                        ],'delete']},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form=layui.form;


            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        edit: function () {
            var form=layui.form;
            form.on('select(factory_id)',function(data){
                $.ajax({
                    type:'get',
                    url:'../Common/getHouseProd',
                    data:{
                        factory_id:data.value
                    },
                    dataType:'json',
                    success:function(res){
                        console.log(res);
                        var select = document.getElementById('houseid1');
                        var options = [];
                        select.innerHTML='';
                        res.forEach(function(item,index){
                            var value = item.id;
                            var text = item.housename;
                            options[index] = document.createElement('option');
                            options[index].value = value;
                            options[index].text = text;
                            select.appendChild(options[index]);
                        })
                        form.render('select');
                    }
                })
            });
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });
        },
        view:function(){
            ea.listen();
        },
        stock:function(){

            ea.table.render({
                init: init_stock,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区',search:false},
                    {field: 'housename', title: '鸡舍',search:false},
                    {field: 'total_num', title: '总数量（枚）',search:false},
                    {field: 'total_weight', title: '总重量('+munit+')',search:false},
                    {field: 'sell_num', title: '出库数量（枚）',search:false},
                    {field: 'sell_weight', title: '出库重量('+munit+')',search:false},
                    {field: 'stock_num', title: '库存数量（枚）',search:false},
                    {field: 'stock_weight', title: '库存重量',search:false},
                ]],
            });
            ea.listen();
        },
        print:function(){
            ea.listen();
        },
        search:function(){
            var form = layui.form;
            form.on('select(factory_id)',function(data){
                zn.getHouseProd(data.value);
            })
            var cols=[[
            {field: 'no', title: '单号',align:"center"},
            {field: 'housename', title: '鸡舍',align:"center"},
            {field: 'class_name', title: '蛋型',align:"center"},
            {field: 'unit_name', title: '规格',align:"center"},
            {field: 'cdate', title: '日期',align:"center"},
            {field: 'num', title: '数量',align:"center"},
        ]];
            zn.doSearch(cols);
            ea.listen();
        },
    };
    var form=layui.form;
    $('#add').on('click',function (){
        index++;
        if($('#factory_id').val()===''){
            layer.alert('需要先选择厂区！');
            return false;
        }
        var house_id = 'houseid'+index;
        var class_id = 'class'+index;
        var unit_id = 'unit'+index;
        var num_id = 'num'+index;
        var div_id = 'div'+index;
        var trdata = '<tr id="' + div_id + '">' +
            '<td id="house"><select name="prod_id[]" lay-verify="required" id="'+house_id+'"></select></td>' +
            '<td id="unit"><select name="unit_id[]" lay-verify="required" id="'+unit_id+'"></select></td>' +
            '<td id="class"><select name="class_id[]" lay-verify="required" id="'+class_id+'"></select></td>' +
            '<td><input type="text" class="layui-input" name="num[]" id="'+num_id+'" placeholder="数量"  onblur="" value=""/></td>' +
            '<td><button type="text" class="layui-btn layui-btn-danger layui-btn-sm" lay-filter="del" onclick="remove(' + index + ');">移除</button></td>' +
            '</tr>';
        $('#table').append(trdata);
        createSelect('houseid1',house_id);
        createSelect('class1',class_id);
        createSelect('unit1',unit_id);
        form.render('select');
    })

    function createSelect(sourceid,selectid) {
        var source = document.getElementById(sourceid);
        var select = document.getElementById(selectid);
        var options = [];
        for (var i = 0; i < source.options.length; i++) {
            var value = source.options[i].value;
            var text = source.options[i].text;
            options[i] = document.createElement('option');
            options[i].value = value;
            options[i].text = text;
            select.appendChild(options[i]);
        }
        // 将select添加到div容器
        //container.appendChild(select);
    }
    return Controller;
});