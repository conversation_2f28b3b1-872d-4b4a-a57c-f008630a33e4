define(["jquery", "easy-admin","znegdoo"], function ($, ea, zn) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'user.custom/index',
        add_url: 'user.custom/add',
        edit_url: 'user.custom/edit',
        delete_url: 'user.custom/delete',
        export_url: 'user.custom/export',
        modify_url: 'user.custom/modify',
    };

    var levelList = {'1': '大客户', '2': '中型客户', '3': '小客户'};

    var Controller = {
        index: function () {
            var workerList = zn.getWorker();
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'unitname', title: '公司名称'},
                    {
                        field: 'type',
                        title: '客户类别',
                        selectList: {0: ' ', '1': '孵化场', '2': '青年鸡场', '3': '商品蛋鸡场', '4': '综合鸡场'}
                    },
                    {field: 'scalename', title: '规模',search: false},
                    {field: 'level', title: '级别', selectList: levelList,search: false},
                    {field: 'name', title: '联系人', search: false},
                    {field: 'phone', title: '联系电话'},
                    {field: 'area', title: '地区', search: false},
                    {field: 'worker_id', title: '负责人',selectList:workerList,templet: function (d){
                            return d.workerName;
                        }},
                    {
                        width: 250, title: '操作', templet: ea.table.tool,
                        operat: [[
                            {
                                text: '生产提醒',
                                url: init.record_url,
                                method: 'open',
                                auth: 'record',
                                class: 'layui-btn layui-btn-xs layui-btn-normal',
                                extend: 'data-full="true"',
                            }
                        ], 'edit', 'delete']
                    },
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };

    return Controller;
});