define(["jquery", "easy-admin", "echarts", "china", "miniAdmin", "miniTab"], function ($, ea, echarts, undefined, miniAdmin, miniTab) {

    var Controller = {

        index: function () {
            //miniTab.listen();
            miniAdmin.listen();
            $('.scrollDiv').liMarquee({
                direction: 'up',//身上滚动
                runshort: false,//内容不足时不滚动
                scrollamount: 20//速度
            });
            $.ajax({
                // 请求方式为get或者post等
                type: "POST",
                // 服务器响应的数据类型
                dataType: "json",
                // 请求的url(一般为后台接口)
                url: "index_echart",
                // 发送到服务器的参数,
                data: {},
                // 请求成功后返回的数据，赋值给变量'data'
                success: function (data) {
                    console.log(data);
                    var xdata = data['xdata'];
                    var swwdData = data['swwdData'];
                    var feedData = data['feedData'];
                    var eggData = data['eggData'];
                    // echart(xdata, swwdData['ymax'], 'echart_wd', 'red', 'aqua', '℃');
                    // echart(xdata, swwdData['ymin'], 'echart_wd', 'blue', 'aqua', '℃');
                    echarts_swwd(swwdData['xdata'], swwdData['ymax'], swwdData['ymin']);
                    echart(xdata, feedData, 'echart_feed', 'blue', 'aqua', 'KG');
                    echart(xdata, eggData, 'echart_egg', 'green', 'aqua', '斤');

                },
                // 请求失败异常后，返回的错误信息
                error: function (err) {

                }
            });

            startClock();
            ea.listen();

        },

        farmview: function () {
            miniTab.listen();
            startClock();
            var houseid = $('#houseid').val();
            $.ajax({
                // 请求方式为get或者post等
                type: "POST",
                // 服务器响应的数据类型
                dataType: "json",
                // 请求的url(一般为后台接口)
                url: "bigview_info_echart",
                // 发送到服务器的参数,
                data: {houseid: houseid},
                // 请求成功后返回的数据，赋值给变量'data'
                success: function (data) {
                    console.log(data);
                    //X轴
                    var xdata = data['xdata'];
                    var growxData = data['growXdata'];
                    //室外温度
                    var swwdData = data['swwdData'];
                    echarts_swwd(xdata, swwdData['ymax'], swwdData['ymin']);
                    //一周用电
                    echart(xdata, data['powerData'], 'echart_power', 'blue', 'aqua', 'kw/h')
                    //一周用水
                    echart(xdata, data['waterData'], 'echart_water', 'green', 'aqua', '吨')
                    //一周用料
                    echart(xdata, data['feedData'], 'echart_feed', 'lime', 'aqua', 'KG');
                    //一周产蛋
                    echart(xdata, data['eggData'], 'echart_egg', 'orange', 'aqua', '斤');
                    //一周料蛋比
                    echart(xdata, data['feedeggData'], 'echart_feedegg', 'orange', 'aqua', '%');
                    //生长曲线-达标率
                    echart(growxData, data['growData'], 'echart_grow' , 'blue' ,'aqua','%');
                },
                // 请求失败异常后，返回的错误信息
                error: function (err) {

                }
            });
            ea.listen();
        },
        envview: function () {
            miniTab.listen();
            startClock();
            var wd1=$("#wd1_val").val();
            var wd2=$("#wd2_val").val();
            var wd3=$("#wd3_val").val();
            var wd4=$("#wd4_val").val();
            var wd5=$("#wd5_val").val();
            var wd6=$("#wd6_val").val();
            var wd7=$("#wd7_val").val();
            var wd8=$("#wd8_val").val();
            var swwd=$("#swwd_val").val();
            var sd=$("#sd_val").val();
            var fy=$("#fy_val").val();
            var co2=$("#co2_val").val();
            var tfl=$("#tfl_val").val();
            // echarts_wd('温度一',wd1,'wd1');
            // echarts_wd('温度二',wd2,'wd2');
            // echarts_wd('温度三',wd3,'wd3');
            // echarts_wd('温度四',wd4,'wd4');
            // echarts_wd('温度五',wd5,'wd5');
            // echarts_wd('温度六',wd6,'wd6');
            // echarts_wd('温度七',wd7,'wd7');
            // echarts_wd('温度八',wd8,'wd8');
            echarts_ybp('温度一',wd1,'wd1','℃',-20,50);
            echarts_ybp('温度二',wd2,'wd2','℃',-20,50);
            echarts_ybp('温度三',wd3,'wd3','℃',-20,50);
            echarts_ybp('温度四',wd4,'wd4','℃',-20,50);
            echarts_ybp('温度五',wd5,'wd5','℃',-20,50);
            echarts_ybp('温度六',wd6,'wd6','℃',-20,50);
            echarts_ybp('温度七',wd7,'wd7','℃',-20,50);
            echarts_ybp('温度八',wd8,'wd8','℃',-20,50);
            echarts_ybp('室外温度',swwd,'swwd','℃',-20,50);
            echarts_ybp('湿度',sd,'sd','%',0,100);
            echarts_ybp('负压',fy,'fy','pa',0,100);
            echarts_ybp('二氧化碳',co2,'co2','ppm',1000,5000);
            echarts_ybp('控制通风量',tfl,'tfl','%',0,100);
            var sn = $('#sn').val();
            $('.scrollDiv').liMarquee({
                direction: 'up',//身上滚动
                runshort: false,//内容不足时不滚动
                scrollamount: 20//速度
            });
            $.ajax({
                // 请求方式为get或者post等
                type: "POST",
                // 服务器响应的数据类型
                dataType: "json",
                // 请求的url(一般为后台接口)
                url: "bigview_env_echart",
                // 发送到服务器的参数,
                data: {sn: sn},
                // 请求成功后返回的数据，赋值给变量'data'
                success: function (data) {
                    console.log(data);
                    //X轴
                    var xdata = data['xdata'];
                    //室外温度
                    var swwdData = data['swwdData'];
                    echarts_swwd(swwdData['xdata'], swwdData['ymax'], swwdData['ymin']);
                    //一周平均温度
                    echart(data['xdata'], data['wdData'], 'echart_wd', 'blue', 'aqua', '°C');
                    //一周湿度
                    echart(data['xdata'], data['sdData'], 'echart_sd', 'orange', 'aqua', '%');
                    //一周CO2
                    echart(data['xdata'], data['co2Data'], 'echart_co2', 'lime', 'aqua', 'PPm');
                    //一周压力
                    echart(data['xdata'], data['ylData'], 'echart_fy', 'green', 'aqua', 'Pa');
                    //温度

                },
                // 请求失败异常后，返回的错误信息
                error: function (err) {

                }
            });
            ea.listen();
        }
    };

    /**
     * 以下为全局函数
     * @param mapChart
     */

    /**
     * 饲料图表,产蛋量图表，其它可共用图表
     * @param xdata X轴数据
     * @param ydata 数据
     * @param id    dom的ID
     * @param color1 渐变起始色
     * @param color2 渐变终止色
     */

    function echart(xdata, ydata, id, color1, color2, unit) {
        var chartDom = document.getElementById(id);
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            grid: {
                left: '10',
                top: 'top',
                right: '20',
                bottom: '0%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                axisLabel: {
                    color: '#eee'
                },
                boundaryGap: false,
                splitLine: {
                    show: true,
                    lineStyle: {
                        // 使用深浅的间隔色
                        color: '#25cfd5',
                        type: 'dashed'
                    }
                },
                data: xdata
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}' + unit,
                    color: '#eee'
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        // 使用深浅的间隔色
                        color: '#25cfd5',
                        type: 'dashed'
                    }
                },
            },
            series: [
                {
                    //name: 'Highest',
                    type: 'line',
                    data: ydata,
                    symbol: 'none', //去掉折线图中的节点
                    smooth: true, //true 为平滑曲线，false为直线
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: color1 // 0% 处的颜色
                            }, {
                                offset: 1, color: color2 // 100% 处的颜色
                            }],
                            global: false // 缺省为 false
                        }

                    }

                }
            ]
        };

        option && myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }
    //室外温度
    function echarts_swwd(xdata, ymax, ymin) {
        var myChart = echarts.init(document.getElementById('echart_swwd'));
        option = {
            tooltip: {

                trigger: 'axis',
                axisPointer: {
                    lineStyle: {
                        color: '#57617B'
                    }
                },
                formatter: '{b}日	:<br/> 室外温度{c}'
            },

            grid: {
                left: '0',
                right: '20',
                top: '10',
                bottom: '0',
                containLabel: true
            },
            xAxis: [{
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(255,255,255,.6)'
                    }
                },
                data: xdata
            }],
            yAxis: [{
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: 'rgba(255,255,255,.6)'
                    }
                },
                axisLine: {
                    show: false,

                },
                splitLine: {
                    lineStyle: {
                        type: 'dotted',
                        color: 'rgba(255,255,255,.1)'
                    }
                }
            }],
            series: [{
                name: '最高温度',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 5,
                showSymbol: false,
                lineStyle: {
                    normal: {
                        width: 2
                    }
                },

                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(98, 201, 141, 0.5)'
                        }, {
                            offset: 1,
                            color: 'rgba(98, 201, 141, 0.1)'
                        }], false),
                        shadowColor: 'rgba(0, 0, 0, 0.1)',
                        shadowBlur: 10
                    }
                },
                itemStyle: {
                    normal: {
                        color: '#4cb9cf',
                        borderColor: 'rgba(98, 201, 141,0.27)',
                        borderWidth: 12
                    }
                },
                data: ymax
            },
                {
                    name: '最低温度',
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 5,
                    showSymbol: false,
                    lineStyle: {
                        normal: {
                            width: 2
                        }
                    },

                    areaStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(98, 201, 141, 0.5)'
                            }, {
                                offset: 1,
                                color: 'rgba(98, 201, 141, 0.1)'
                            }], false),
                            shadowColor: 'rgba(0, 0, 0, 0.1)',
                            shadowBlur: 10
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#4cb9cf',
                            borderColor: 'rgba(98, 201, 141,0.27)',
                            borderWidth: 12
                        }
                    },
                    data: ymin
                }
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }

    //温度仪表盘
    function echarts_wd(name,val,emid) {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById(emid));
        option = {
            series: [
                {
                    type: 'gauge',
                    radius:'100%',
                    center: ['50%', '60%'],
                    startAngle: 215,
                    endAngle: -35,
                    min: 0,
                    max: 40,
                    splitNumber: 8,
                    itemStyle: {
                        //color: '#48a9dd'
                    },
                    progress: {
                        show: true,
                        width: 5
                    },
                    pointer: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            width: 5,
                            color:[[1,'#2d4e7d']]
                        }
                    },
                    axisTick: {
                        distance: 0,
                        splitNumber: 1,
                        length:2,
                        lineStyle: {
                            width: 1,
                            color: '#999'
                        }
                    },
                    splitLine: {
                        distance: 0,
                        length: 1,
                        lineStyle: {
                            width: 1,
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        distance: 0,
                        color: '#999',
                        fontSize: 4,
                        padding:8
                    },
                    anchor: {
                        show: false
                    },
                    title: {
                        show: true,
                        textStyle:{
                            fontSize:10,
                            color:'#77caf7'
                        },
                        offsetCenter: [0, '50%']
                    },
                    detail: {
                        valueAnimation: true,
                        width: '60%',
                        lineHeight: 40,
                        borderRadius: 8,
                        offsetCenter: [0, '-1%'],
                        fontSize: '40%',
                        fontWeight: 'bolder',
                        formatter: '{value} °C',
                        color: '#ffffff'
                    },
                    data: [
                        {
                            value: val,
                            name:name
                        }
                    ]
                }
            ]
        };
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }
    //湿度、压力等仪表盘
    function echarts_ybp(name,val,emid,inc,min,max) {
        var myChart = echarts.init(document.getElementById(emid));
        option = {
            series: [
                {
                    type: 'gauge',
                    radius:'100%',
                    min: min,
                    max: max,
                    splitNumber: 10,
                    itemStyle: {
                        //color: '#ff0000'
                    },
                    progress: {
                        show: true,
                        width: 5
                    },
                    pointer: {
                        icon: 'path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z',
                        show: true,
                        width:2
                    },
                    axisLine: {
                        lineStyle: {
                            width: 5,
                            color:[[1,'#2d4e7d']]
                        },
                    },
                    axisTick: {
                        distance: 0,
                        splitNumber: 1,
                        length:2,
                        lineStyle: {
                            width: 1,
                            color: '#999'
                        }
                    },
                    splitLine: {
                        distance: 0,
                        length: 1,
                        lineStyle: {
                            width: 1,
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        distance: 0,
                        color: '#999',
                        fontSize: 4,
                        padding:8
                    },
                    anchor: {
                        show: true,
                        showAbove: true,
                        size: 3,
                        itemStyle: {
                            borderWidth: 3
                        }
                    },
                    title: {
                        show: true,
                        textStyle:{
                            fontSize:10,
                            color:'#77caf7'
                        },
                        offsetCenter: [0, '30%']
                    },
                    detail: {
                        valueAnimation: true,
                        fontSize: '40%',
                        color:"#FFFFFF",
                        offsetCenter: [0, '70%'],
                        formatter: '{value} '+inc,
                    },
                    data: [
                        {
                            value: val,
                            name:name
                        }
                    ]
                }
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }

    return Controller;
});

//动态时钟
function startClock() {
    var time = new Date();
    var years = displayClock(time.getFullYear()) + "年";
    var months = displayClock(time.getMonth() + 1) + "月";
    var days = displayClock(time.getDate()) + "日 ";
    var hours = displayClock(time.getHours()) + ":";
    var minutes = displayClock(time.getMinutes()) + ":";
    var seconds = displayClock(time.getSeconds());
    +" ";
    var weekstr = "日一二三四五六";
    var weekNum = time.getDay();
    var weeks = " 星期" + weekstr.substr(weekNum, 1);
    //显示时间
    var datetime = document.getElementById("datetime");
    var str = years + months + days + hours + minutes + seconds + weeks;
    datetime.innerHTML = str;
    timer = setTimeout("startClock()", 1000);//延时器
}

function displayClock(num) {
    if (num < 10) {
        return "0" + num;
    } else {
        return num;
    }
}