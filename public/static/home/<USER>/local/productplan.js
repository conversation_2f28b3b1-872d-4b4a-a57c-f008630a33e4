define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.productplan/index',
        add_url: 'local.productplan/add',
        edit_url: 'local.productplan/edit',
        delete_url: 'local.productplan/delete',
        export_url: 'local.productplan/export',
        modify_url: 'local.productplan/modify',
    };

    var Controller = {

        index: function () {
            var id=getQueryVariable("id");
            $.ajax({
                url:'../Common/getPlanHouse',
                data:{id:id},
                type:'get',
                dataType:'json',
                success:res=>{
                    console.log(res);
                    $('#title').html(res+'生产计划');
                }
            })
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url+'?id='+id,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="false"',
                    }],'delete'],
                where: {id: id},
                cols: [[
                    {type: 'checkbox'},
                    {field: 'cdate', title: '日期',search:'range'},
                    {field: 'days', title: '日龄'},
                    {field: 'content', title: '计划内容',search:false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var id=getQueryVariable("id");
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });

        },
        edit: function () {
            var id=getQueryVariable("id");
            ea.listen(function(data){
                var date=new Date(ea.formatDate(data.cdate));
                data.cdate=date.getTime()/1000;
                return data;
            });

        },
    };
    return Controller;
});
//接收页面传值的方法
function getQueryVariable(variable){
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
        var pair = vars[i].split("=");
        if(pair[0] == variable){return pair[1];}
    }
    return(false);
}