define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.dealersale/index',
        add_url: 'local.dealersale/add',
        edit_url: 'local.dealersale/edit',
        delete_url: 'local.dealersale/delete',
        export_url: 'local.dealersale/export',
        modify_url: 'local.dealersale/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'dealerCustom.unitname', title: '公司名称'},
                    {field: 'product', title: '产品名称'},
                    {field: 'custom_id', title: '客户编号'},
                    {field: 'devnum', title: '设备数量'},
                    {field: 'sale_date', title: '销售日期'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});