define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.kaoqin/index',
        add_url: 'local.kaoqin/add',
        edit_url: 'local.kaoqin/edit',
        delete_url: 'local.kaoqin/delete',
        export_url: 'local.kaoqin/export',
        modify_url: 'local.kaoqin/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    // {field: 'id', title: 'id',search: false},
                    {field: 'userWorker.name', title: '姓名'},
                    {field: 'check_date', title: '日期'},
                    {field: 'results', title: '考勤结果',selectList:{0:'缺勤',1:'正常',2:'请假'}},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});