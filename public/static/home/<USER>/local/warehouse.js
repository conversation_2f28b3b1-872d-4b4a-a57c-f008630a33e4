define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.warehouse/index',
        add_url: 'local.warehouse/add',
        edit_url: 'local.warehouse/edit',
        delete_url: 'local.warehouse/delete',
        export_url: 'local.warehouse/export',
        modify_url: 'local.warehouse/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factory.factoryname', minWidth:120, title: '厂区'},
                    {field: 'type', minWidth:120, search: 'select', selectList: {"1":"原料仓库","2":"饲料仓库","3":"药品仓库","4":"生物制品","5":"鸡蛋仓库","6":"易耗品"}, title: '类型'},
                    {field: 'name', minWidth:120, title: '仓库名称'},
                    {field: 'note', title: '说明',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});