define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.department/index',
        add_url: 'local.department/add',
        edit_url: 'local.department/edit',
        delete_url: 'local.department/delete',
        export_url: 'local.department/export',
        modify_url: 'local.department/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'id'},
                    {field: 'farmid', title: '客户编号'},
                    {field: 'name', title: '部门名称'},
                    {field: 'pid', title: '上级部门'},
                    {field: 'manager', title: '主管-worker'},
                    {field: 'phone', title: '部门电话'},
                    {field: 'notes', title: '部门简介'},
                    {field: 'create_time', title: 'create_time'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});