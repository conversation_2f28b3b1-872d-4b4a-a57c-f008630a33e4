define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.article/index',
        add_url: 'local.article/add',
        edit_url: 'local.article/edit',
        delete_url: 'local.article/delete',
        export_url: 'local.article/export',
        modify_url: 'local.article/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    // {field: 'id', title: 'ID', width: 80,search: false},
                    {field: 'title', title: '标题', minWidth: 80,align:'left'},
                    {field: 'author', title: '作者', width: 120,search: false},
                    {field: 'create_time', title: '时间', width: 200,search: 'range'},
                ]],
            });
            //触发行单击事件
            var table = layui.table;
            table.on('row', function (obj) {
                var data = obj.data;
                var content = data.content;
                var title = data.title;
                var createTime = data.create_time;
                var author = data.author;
                //1.首先动态创建一个容器标签元素，如DIV
                var temp = document.createElement("div");
                //2.然后将要转换的字符串设置为这个元素的innerHTML(ie，火狐，google都支持)
                temp.innerHTML = content;
                //3.最后返回这个元素的innerText或者textContent，即得到经过HTML解码的字符串了。
                var content = temp.innerText || temp.textContent;

                var html = '<div style="padding:15px 20px; text-align:justify; line-height: 22px;border-bottom:1px solid #e2e2e2;background-color: #2f4056;color: #ffffff">\n' +
                    '<div style="text-align: center;margin-bottom: 20px;font-weight: bold;border-bottom:1px solid #718fb5;padding-bottom: 5px"><h2 class="text-danger">' + title + '</h2><h4>'+author+'</h4></div>\n' +
                    '<div>' + content + '</div>\n' +
                    '</div>\n';

                var index = layer.open({
                    type: 1,
                    title: '公告' + '<span style="float: right;right: 1px;font-size: 12px;color: #b1b3b9;margin-top: 1px">' + createTime + '</span>',
                    area: '70%',
                    shade: 0.8,
                    id: 'layuimini-notice',
                    btn: ['关闭'],
                    btnAlign: 'c',
                    moveType: 1,
                    content: html,

                });
            });
            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});