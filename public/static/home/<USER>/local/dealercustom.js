define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.dealercustom/index',
        add_url: 'local.dealercustom/add',
        edit_url: 'local.dealercustom/edit',
        delete_url: 'local.dealercustom/delete',
        export_url: 'local.dealercustom/export',
        modify_url: 'local.dealercustom/modify',
        invite_url:'local.dealercustom/invitecode',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'userInfo.lxname', title: '姓名'},
                    {field: 'userInfo.farmname', title: '养殖场'},
                    {field: 'province', title: '省份'},
                    {field: 'city', title: '城市'},
                    {field: 'county', title: '区县'},
                    {field: 'userInfo.address', title: '地址'},
                    {field: 'userInfo.lxtel', title: '电话'},
                    {field: 'userInfo.longitude', title: '经度'},
                    {field: 'userInfo.latitude', title: '纬度'},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        }
    };
    return Controller;
});