define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.worker/index',
        add_url: 'local.worker/add',
        edit_url: 'local.worker/edit',
        delete_url: 'local.worker/delete',
        export_url: 'local.worker/export',
        modify_url: 'local.worker/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'name', title: '姓名'},
                    {field: 'sex', title: '姓别',selectList:{1:'男',2:'女'}},
                    {field: 'workdate', title: '入职时间',search: false},
                    {field: 'sfzh', title: '身份证号'},
                    {field: 'jobs.jobs_name', title: '岗位',search: false},
                    {field: 'phone', title: '电话'},
                    {field: 'address', title: '住址',search: false},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            var form = layui.form;
            form.verify({
                name:[/^[\u4E00-\u9FA5A-Za-z]{2,20}$/,"只能填写中文或英文字母,且在2到20个字符之间"],
            });
            ea.listen();
        },
        edit: function () {
            var form = layui.form;
            form.verify({
                name:[/^[\u4E00-\u9FA5A-Za-z]{2,20}$/,"只能填写中文或英文字母，且在2到20个字符之间"],
            });
            ea.listen();
        },
    };
    return Controller;
});