define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.factory/index',
        add_url: 'local.factory/add',
        edit_url: 'local.factory/edit',
        delete_url: 'local.factory/delete',
        export_url: 'local.factory/export',
        modify_url: 'local.factory/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'factoryname', title: '厂区名称'},
                    {field: 'type', title: '类型',selectList:{'0':'混合厂','1':'蛋鸡厂','2':'育雏厂','3':'青年鸡厂','4':'饲料厂'},search:false},
                    {field: 'address', title: '位置',search:false},
                    {field: 'prefix', title: '单号前缀',search:false},
                    {field: 'worker.name', title: '主管，负责人',search:false,templet:function (d){
                        return d.worker_id==0?'':d.worker.name;
                        }},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});