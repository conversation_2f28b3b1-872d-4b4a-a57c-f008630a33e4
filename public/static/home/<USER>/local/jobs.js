define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.jobs/index',
        add_url: 'local.jobs/add',
        edit_url: 'local.jobs/edit',
        delete_url: 'local.jobs/delete',
        export_url: 'local.jobs/export',
        modify_url: 'local.jobs/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {field: 'jobs_name',minWidth:'100', title: '岗位名称'},
                    {field: 'notes', title: '岗位说明'},
                    {width: '250', title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});