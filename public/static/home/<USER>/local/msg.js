define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.msg/index',
        add_url: 'local.msg/add',
        edit_url: 'local.msg/edit',
        delete_url: 'local.msg/delete',
        export_url: 'local.msg/export',
        modify_url: 'local.msg/modify',
        setreader_url: 'local.msg/setreader'
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh',
                    [{
                    title: '置为已读',
                    url: init.setreader_url,
                    method: 'request',
                    auth: 'setreader',
                    class: 'layui-btn layui-btn-normal layui-btn-sm',
                    icon: 'fa fa-paint-brush ',
                    checkbox:true,
                }],'delete'],
                cols: [[
                    {type: 'checkbox'},
                    // {field: 'id', title: 'ID', width: 80,search: false},
                    {field: 'title', title: '标题', minWidth: 80, align: 'left'},
                    {field: 'sender', title: '发送', width: 120,search: false},
                    {field: 'content', title: '内容', minWidth: 200,search: false},
                    {field: 'isreader', title: '状态', minWidth: 80, selectList: {0: "未读", 1: "已读"}},
                    {field: 'create_time', title: '时间', width: 200,search: 'range'},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});