define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'local.sellconfig/index',
        add_url: 'local.sellconfig/add',
        edit_url: 'local.sellconfig/edit',
        delete_url: 'local.sellconfig/delete',
        export_url: 'local.sellconfig/export',
        modify_url: 'local.sellconfig/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'sellunit', title: '销售单元'},
                    {field: 'unitnum', title: '每单元换算数量'},
                    {field: 'munit', title: '换算计量单位'},
                    {width: 250, title: '操作', templet: ea.table.tool},
                ]],
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});