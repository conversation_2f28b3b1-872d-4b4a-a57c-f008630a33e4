<?php /*a:2:{s:60:"D:\Work\hksystem\app\home\view\envmon\realtimedata\info.html";i:1752178899;s:50:"D:\Work\hksystem\app\home\view\layout\default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<style>
    .devbj {
        background: url(/static/home/<USER>/kongbj.png) no-repeat;
        height: 180px;
        width:130px;
    }
    .hjval{
        width:130px;
        font-size: 24px;
        font-family: 黑体;
        text-align: center;
        padding-top: 20px;
        color: #ff7e00;
    }
    .hjval2{
        width:130px;
        font-size: 20px;
        font-family: 黑体;
        text-align: center;
        padding-top: 5px;
        color: #ff7e00;
    }
    .devimg{
        height: 100px;
        width:120px;
        text-align: center;
        padding-top: 25px;
        position: relative;
        margin:0 auto;
    }
    .devtext {
        font-family: 黑体;
        font-size: 22px;
        color: #FFF;
        text-align: center;
        height: 35px;
        line-height: 35px;
    }
    .lbh-title{
        font-family: 黑体;
        font-weight: bold;
        font-size:16px;
    }
    .lbh-img{
        padding-top: 20px;
        width: 130px;
        text-align: center;
    }
    .lbh-val{
        font-size: 20px;height:40px;line-height: 40px;text-align:center;padding-top: 10px;
    }
    .lbh-name{
        height:30px;line-height:30px;color:#FFFFFF;font-size:20px;text-align:center;margin-top:2s0px;
    }
    .lbh-sub-title{
        display: flex;
        justify-content: space-between;
    }
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="layui-elem-field layui-field-title">
            <legend><?php echo htmlentities($farmname); ?></legend>
        </fieldset>
        <blockquote class="layui-elem-quote lbh-sub-title">
            <div><?php echo htmlentities($status); ?></div>
            <div>日龄：<?php echo htmlentities($days); ?>天</div>
        </blockquote>
        <div style="padding: 20px; background-color: #F2F2F2;">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header lbh-title">环境信息</div>
                        <?php if($version==0): ?>
                        <!--旧设备-->
                        <div class="layui-card-body">
                            <div class="layui-fluid">
                                <div class="layui-row layui-col-space3">
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/wd1.png) no-repeat;height: 180px;"
                                         id="wd1">
                                        <?php echo htmlentities((isset($row['wd1']) && ($row['wd1'] !== '')?$row['wd1']:'')); ?>℃
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/wd2.png) no-repeat;height: 180px;"
                                         id="wd2">
                                        <?php echo htmlentities($row['wd2']); ?>℃
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/wd3.png) no-repeat;height: 180px;"
                                         id="wd3">
                                        <?php echo htmlentities($row['wd3']); ?>℃
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/sd.png) no-repeat;height: 180px;"
                                         id="sd">
                                        <?php echo htmlentities($row['sd']); ?>%
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/swwd.png) no-repeat;height: 180px;"
                                         id="swwd">
                                        <?php echo htmlentities($row['swwd']); ?>℃
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/mbwd.png) no-repeat;height: 180px;"
                                         id="mbwd">
                                        <?php echo htmlentities($row['mbwd']); ?>℃
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/kqzl.png) no-repeat;height: 180px;"
                                         id="kqzl">
                                        <?php echo htmlentities($row['kqzl']); ?>级
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/co2.png) no-repeat;height: 180px;"
                                         id="co2">
                                        <?php echo htmlentities($row['co2']); ?>PPM
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/sbll.png) no-repeat;height: 180px;"
                                         id="sbll">
                                        <?php echo htmlentities($row['sbll']); ?>M3
                                    </div>
                                    <div class="layui-col-md1 hjval"
                                         style="background: url(/static/home/<USER>/yl.png) no-repeat;height: 180px;"
                                         id="yl">
                                        <?php echo htmlentities($row['yl']); ?>Pa
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php else: ?>
                        <!--新设备信息-->
                        <div class="layui-card-body">
                            <div class="layui-fluid">
                                <div class="layui-row layui-col-space3">
                                    <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if($vo['itemtype']==1): ?>
                                            <div class="layui-col-md1 devbj">
                                                <div class="lbh-img"><img src="/static/home/<USER>/<?php echo htmlentities($vo['imgfile']); ?>" height="60px"/></div>
                                                <div class="lbh-val">
                                                    <?php echo htmlentities($vo['itemval']); ?>
                                                </div>
                                                <div class="lbh-name">
                                                    <?php echo htmlentities($vo['itemname_c']); ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header lbh-title">设备状态</div>
                        <?php if($version==0): ?>
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space3">
                                <?php $__FOR_START_1659692150__=1;$__FOR_END_1659692150__=13;for($i=$__FOR_START_1659692150__;$i < $__FOR_END_1659692150__;$i+=1){ ?>
                                <div class="layui-col-md1 devbj" id="fj<?php echo htmlentities($i); ?>">
                                    <div class="devimg">
                                        <?php if($row["fj".$i]==1): ?><img src="/static/home/<USER>/fj1.gif" /><?php else: ?><img src="/static/home/<USER>/fj0.gif" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">风机<?php echo htmlentities($i); ?></div>
                                </div>
                                <?php } ?>
                                <div class="layui-col-md1 devbj" id="sl1">
                                    <div class="devimg">
                                        <?php if($row["sl"]==1): ?><img src="/static/home/<USER>/sl_work.png" /><?php else: ?><img src="/static/home/<USER>/sl_rest.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">湿帘一</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="sl2">
                                    <div class="devimg">
                                        <?php if($row["sl2"]==1): ?><img src="/static/home/<USER>/sl_work.png" /><?php else: ?><img src="/static/home/<USER>/sl_rest.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">湿帘二</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="jr1">
                                    <div class="devimg">
                                        <?php if($row["jr1"]==1): ?><img src="/static/home/<USER>/jr_work.png" /><?php else: ?><img src="/static/home/<USER>/jr_rest.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">加热一</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="jr2">
                                    <div class="devimg">
                                        <?php if($row["jr2"]==1): ?><img src="/static/home/<USER>/jr_work.png" /><?php else: ?><img src="/static/home/<USER>/jr_rest.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">加热二</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="jr3">
                                    <div class="devimg">
                                        <?php if($row["jr3"]==1): ?><img src="/static/home/<USER>/jr_work.png" /><?php else: ?><img src="/static/home/<USER>/jr_rest.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">加热三</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="zm1">
                                    <div class="devimg">
                                        <?php if($row["zm1"]==1): ?><img src="/static/home/<USER>/zm1.gif" /><?php else: ?><img src="/static/home/<USER>/zm0.gif" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">照明一</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="zm2">
                                    <div class="devimg">
                                        <?php if($row["zm2"]==1): ?><img src="/static/home/<USER>/zm1.gif" /><?php else: ?><img src="/static/home/<USER>/zm0.gif" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">照明二</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="xcjd">
                                    <div class="devimg">
                                        <?php if($row["xcjd"]>0): ?><div><img src="/static/home/<USER>/jd1.png" /></div><div class="hjval2"><?php echo htmlentities($row['xcjd']); ?>°</div><?php else: ?><img src="/static/home/<USER>/jd0.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">小窗角度</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="dlbjd">
                                    <div class="devimg">
                                        <?php if($row["dlbjd"]>0): ?><div><img src="/static/home/<USER>/jd1.png" /></div><div class="hjval2"><?php echo htmlentities($row['dlbjd']); ?>°</div><?php else: ?><img src="/static/home/<USER>/jd0.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">导流板角度</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="bpq1">
                                    <div class="devimg">
                                        <?php if($row["bpq1"]==100): ?><img src="/static/home/<USER>/bpq1.png" /><?php else: ?><img src="/static/home/<USER>/bpq0.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">变频器一</div>
                                </div>
                                <div class="layui-col-md1 devbj" id="bpq2">
                                    <div class="devimg">
                                        <?php if($row["bpq2"]==100): ?><img src="/static/home/<USER>/bpq1.png" /><?php else: ?><img src="/static/home/<USER>/bpq0.png" /><?php endif; ?>
                                    </div>
                                    <div class="devtext">变频器二</div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space3">
                                <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if($vo['itemtype']==2): ?>
                                        <div class="layui-col-md1 devbj">
                                            <div class="lbh-img"><img src="/static/home/<USER>/<?php echo htmlentities($vo['imgfile']); ?>" height="60px"/></div>
                                            <div class="lbh-val">
                                                <?php echo htmlentities($vo['itemval']); ?>
                                            </div>
                                            <div class="lbh-name">
                                                <?php echo htmlentities($vo['itemname_c']); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
</body>
</html>