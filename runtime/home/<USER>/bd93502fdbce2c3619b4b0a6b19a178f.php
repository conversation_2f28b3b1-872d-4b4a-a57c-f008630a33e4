<?php /*a:2:{s:54:"/www/wwwroot/hksystem/app/home/<USER>/bigview/index.html";i:1752393401;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($farmname); ?></title>
   <script type="text/javascript" src="/static/plugs/bigview-main/js/jquery.js"></script>
    <!--<script type="text/javascript" src="/static/plugs/bigview-main/js/echarts1.min.js"></script>-->
    <script src="/static/plugs/bigview-main/js/jquery.liMarquee.js?v=<?php echo time().'limarquee'; ?>"></script>
    <!--<script language="JavaScript" src="/static/plugs/bigview-main/js/js.js"></script>-->
    <script type="text/javascript" src="/static/plugs/bigview-main/js/jquery.countup.min.js?v=<?php echo time(); ?>"></script>
    <script type="text/javascript" src="/static/plugs/bigview-main/js/jquery.waypoints.min.js?v=<?php echo time(); ?>"></script>
    <link rel="stylesheet" href="/static/plugs/bigview-main/css/comon0.css?v=<?php echo time(); ?>">
</head>
<body>
<div class="loading">
    <div class="loadbox"><img src="/static/plugs/bigview-main/images/loading.gif"> 页面加载中...</div>
</div>

<div class="head">
    <h1 style="font-size: .30rem;padding-left: 20px;"><?php echo htmlentities($farmname); ?></h1>
</div>
<div class="mainbox">
    <ul class="clearfix">
        <li>
            <div class="boxall" style="height:calc(33.3333% - .25rem)">
                <div class="tit01">一周室外温度</div>
                <div class="boxnav nav01" id="echart_swwd"></div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height:calc(33.3333% - .25rem); margin-bottom: .25rem;">
                <div class="tit01">一周用料</div>
                <div class="boxnav nav01" id="echart_feed"></div>
                <div class="boxfoot"></div>
            </div>

            <div class="boxall" style="height:calc(33.3333% - .25rem)">
                <div class="tit01">一周产蛋</div>
                <div class="boxnav nav01" id="echart_egg"></div>
                <div class="boxfoot"></div>
            </div>
        </li>
        <li>
            <div class="tit02" id="datetime">时间</div>
            <div class="boxall" style="height:calc(33.33333% - .25rem)">

                <div class="boxnav nav02" style="justify-content: space-around">
                    <div class="cunlan med">
                        <p class="p1"><?php echo htmlentities($totalData['cunlan']); ?></p>
                        <p class="p4">存栏数量</p>
                    </div>
                    <div class="feed med">
                        <p class="p1"><?php echo htmlentities($totalData['feednum']); ?>Kg</p>
                        <p class="p4">月度用料</p>
                    </div>
                    <div class="power med">
                        <p class="p1"><?php echo htmlentities($totalData['power']); ?>度</p>
                        <p class="p4">月度用电</p>
                    </div>
                    <div class="water med">
                        <p class="p1"><?php echo htmlentities($totalData['water']); ?>吨</p>
                        <p class="p4">月度用水</p>
                    </div>
                    <div class="egg med">
                        <p class="p1"><?php echo htmlentities($totalData['eggweight']); ?>斤</p>
                        <p class="p4">月度产蛋</p>
                    </div>
                </div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height:calc(66.666666% - .25rem)">
                <div class="tit03">鸡舍数据</div>
                <div class="boxnav nav03">
                    <div class="listhead listhead1">
                        <span>鸡舍名称</span>
                        <span>品种</span>
                        <span>日龄</span>
                        <span>存栏数量</span>
                        <span>操作</span>
                    </div>
                    <div class="listnav listnav1 scrollDiv" style="height:calc(95% - .25rem);">
                        <ul>
                            <?php if(is_array($houseList) || $houseList instanceof \think\Collection || $houseList instanceof \think\Paginator): $i = 0; $__LIST__ = $houseList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li><span><?php echo htmlentities($vo['housename']); ?></span>
                                <span><?php echo htmlentities($vo['typename']['typename']); ?></span>
                                <span><?php echo htmlentities($vo['days']); ?></span>
                                <span><?php echo htmlentities($vo['cunlan']); ?></span>
                                <span>
                                    <a href="<?php echo url('bigview/envview',['houseid'=>$vo['houseid'],'sn'=>$vo['sn']]); ?>" target="_blank" style="background-color: #ff6633;padding:2px 8px;margin:5px 10px;color:#FFFFFF"> 查看</a>
                                </span>
                            </li>

                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </div>
                </div>
                <div class="boxfoot"></div>
            </div>
        </li>
        <li>
            <div class="boxall" style="height:calc(33.3333% - .25rem);">
                <div class="tit04">药品/生物制剂过期预警</div>
                <div class="boxnav nav04">
                    <div class="listhead listhead2">
                        <span>名称</span>
                        <span>当前状态</span>
                        <span>过期时间</span>
                    </div>

                    <div class="listnav listnav2 scrollDiv">
                        <ul class="smjl">
                            <?php if(is_array($warnList) || $warnList instanceof \think\Collection || $warnList instanceof \think\Paginator): $i = 0; $__LIST__ = $warnList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li>
                                <span><?php echo htmlentities((isset($vo['name']) && ($vo['name'] !== '')?$vo['name']:'')); ?></span>
                                <span><?php echo htmlentities((isset($vo['status']) && ($vo['status'] !== '')?$vo['status']:'')); ?></span>
                                <span class="text-green"><?php echo htmlentities((isset($vo['edate']) && ($vo['edate'] !== '')?$vo['edate']:'')); ?> </span>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </div>
                </div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height:calc(33.3333% - .25rem);">
                <div class="tit04">库存预警</div>
                <div class="boxnav nav04">
                    <div class="listhead listhead2">
                        <span style="text-align: left">名称</span>
                        <span>当前库存</span>
                        <span>预警量</span>
                    </div>

                    <div class="listnav listnav2 scrollDiv">

                        <ul class="smjl">
                            <?php if(is_array($stockList) || $stockList instanceof \think\Collection || $stockList instanceof \think\Paginator): $i = 0; $__LIST__ = $stockList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li>
                                <span style="text-align: left"><?php echo htmlentities((isset($vo['name']) && ($vo['name'] !== '')?$vo['name']:'')); ?></span>
                                <span style="width:25%"><?php echo htmlentities((isset($vo['num']) && ($vo['num'] !== '')?$vo['num']:'')); ?></span>
                                <span class="text-green"><?php echo htmlentities((isset($vo['warn']) && ($vo['warn'] !== '')?$vo['warn']:'')); ?><?php echo htmlentities((isset($vo['munit']) && ($vo['munit'] !== '')?$vo['munit']:'')); ?></span>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </div>
                </div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height:calc(33.3333% - .25rem);">
                <div class="tit04">生产异常报警</div>
                <div class="boxnav nav04">
                    <div class="listhead listhead2">
                        <span>报警信息</span>
                        <span>时间</span>
                    </div>

                    <div class="listnav listnav2 scrollDiv">
                        <ul class="smjl">
                            <?php if(is_array($warningList) || $warningList instanceof \think\Collection || $warningList instanceof \think\Paginator): $i = 0; $__LIST__ = $warningList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li>
                                <span><?php echo htmlentities($vo['content']); ?></span>
                                <span class="text-green"><?php echo htmlentities($vo['createTime']); ?></span>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </div>
                </div>
                <div class="boxfoot"></div>
            </div>
        </li>
    </ul>
</div>



<script type="text/javascript">
    $('.counter').countUp();
    $(function () {

    });

</script>

<!--<script src="/static/plugs/bigview-main/js/countDown.js?v=<?php echo time(); ?>"></script>-->
<script type="text/javascript">
    $(window).load(function () {
        $(".loading").fadeOut()
    })

    /****/
    /****/
    $(document).ready(function () {
        var whei = $(window).width()
        $("html").css({fontSize: whei / 20})
        $(window).resize(function () {
            var whei = $(window).width()
            $("html").css({fontSize: whei / 20})
        });
    });

    $("input[name='countDown']").each(function () {
        var time_end = this.value;
        var con = $(this).next("span");
        var _ = this.dataset;
        countDown(con, {
            time_end: time_end//开始时间
        })
            //提供3个事件分别为:启动,重启,停止
            .on("countDownStarted countDownRestarted  countDownEnded ", function (arguments) {
                console.info(arguments);
            });
    });

</script>
</body>
</html>


</body>
</html>