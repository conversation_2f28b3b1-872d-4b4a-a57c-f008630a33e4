<?php /*a:2:{s:63:"/www/wwwroot/hksystem/app/home/<USER>/product/henshouse/edit.html";i:1729492606;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">

        <div class="layui-form-item">
            <label class="layui-form-label">鸡舍名称</label>
            <div class="layui-input-block">
                <input type="text" name="housename" class="layui-input" lay-verify="required" placeholder="请输入鸡舍名称" value="<?php echo htmlentities((isset($row['housename']) && ($row['housename'] !== '')?$row['housename']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">厂区名称</label>
            <div class="layui-input-block">
            <select name="factory_id" lay-verify="required" data-select="<?php echo url('Common/factoryList'); ?>" data-fields="id,factoryname" data-value="<?php echo htmlentities((isset($row['factory_id']) && ($row['factory_id'] !== '')?$row['factory_id']:'')); ?>">
            </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">建设日期</label>
            <div class="layui-input-block">
                <input type="text" name="createdate" data-date="" data-date-type="date" class="layui-input" lay-verify="required" placeholder="请输入建设日期" value="<?php echo htmlentities((isset($row['createdate']) && ($row['createdate'] !== '')?$row['createdate']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">设计容量</label>
            <div class="layui-input-block">
                <input type="text" name="size" class="layui-input" lay-verify="required|number" placeholder="请输入设计容量" value="<?php echo htmlentities((isset($row['size']) && ($row['size'] !== '')?$row['size']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">类别</label>
            <div class="layui-input-block">
                <select name="lb" lay-verify="required">
                    <?php if(is_array($typeList) || $typeList instanceof \think\Collection || $typeList instanceof \think\Paginator): $i = 0; $__LIST__ = $typeList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($key); ?>" <?php if($row['lb']==$key): ?>selected<?php endif; ?>><?php echo htmlentities($vo); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">长度（米）</label>
            <div class="layui-input-inline">
                <input type="text" name="house_length" class="layui-input" lay-verify="integer" placeholder="请输入长度（米）" value="<?php echo htmlentities((isset($row['house_length']) && ($row['house_length'] !== '')?$row['house_length']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">宽度（米）</label>
            <div class="layui-input-inline">
                <input type="text" name="house_width" class="layui-input" lay-verify="integer" placeholder="请输入宽度（米）" value="<?php echo htmlentities((isset($row['house_width']) && ($row['house_width'] !== '')?$row['house_width']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">高度（米）</label>
            <div class="layui-input-inline">
                <input type="text" name="house_height" class="layui-input" lay-verify="integer" placeholder="请输入高度（米）" value="<?php echo htmlentities((isset($row['house_height']) && ($row['house_height'] !== '')?$row['house_height']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">鸡舍结构</label>
            <div class="layui-input-block">
                <input type="text" name="strcuture" class="layui-input" placeholder="请输入鸡舍结构" value="<?php echo htmlentities((isset($row['strcuture']) && ($row['strcuture'] !== '')?$row['strcuture']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否封闭鸡舍</label>
            <div class="layui-input-block">
                <input type="checkbox" name="isfengbi" lay-skin="switch" lay-text="是|否"  <?php if($row['isfengbi']==1): ?>checked<?php endif; ?>>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否新舍</label>
            <div class="layui-input-block">
                <input type="checkbox" name="isnew" lay-skin="switch" lay-text="是|否" <?php if($row['isnew']==1): ?>checked<?php endif; ?>>
            </div>
        </div>

<!--        <div class="layui-form-item">-->
<!--            <label class="layui-form-label">鸡架列数</label>-->
<!--            <div class="layui-input-block">-->
<!--                <input type="text" name="jijia_col" class="layui-input" placeholder="请输入鸡架列数" value="<?php echo htmlentities((isset($row['jijia_col']) && ($row['jijia_col'] !== '')?$row['jijia_col']:'')); ?>">-->
<!--            </div>-->
<!--        </div>-->
        <div class="layui-form-item">
            <label class="layui-form-label">鸡笼样式</label>
            <div class="layui-input-block">
                <input type="text" name="jijia_style" class="layui-input"  placeholder="请输入鸡架样式" value="<?php echo htmlentities((isset($row['jijia_style']) && ($row['jijia_style'] !== '')?$row['jijia_style']:'')); ?>">
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注信息</label>
            <div class="layui-input-block">
                <textarea name="remark" class="layui-textarea" placeholder="请输入备注信息"><?php echo htmlentities((isset($row['remark']) && ($row['remark'] !== '')?$row['remark']:'')); ?></textarea>
            </div>
        </div>

        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>确认</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
        </div>

    </form>
</div>

</body>
</html>