<?php /*a:2:{s:56:"/www/wwwroot/hksystem/app/home/<USER>/bigview/envview.html";i:1729492604;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($data['housename']); ?></title>
    <script type="text/javascript" src="/static/plugs/bigview-main/js/jquery.js"></script>
    <!--<script type="text/javascript" src="/static/plugs/bigview-main/js/echarts1.min.js"></script>-->
    <script src="/static/plugs/bigview-main/js/jquery.liMarquee.js?v=<?php echo time().'limarquee'; ?>"></script>
    <!--<script language="JavaScript" src="/static/plugs/bigview-main/js/js.js"></script>-->
    <script type="text/javascript" src="/static/plugs/bigview-main/js/jquery.countup.min.js?v=<?php echo time().'countup'; ?>"></script>
    <script type="text/javascript" src="/static/plugs/bigview-main/js/jquery.waypoints.min.js?v=<?php echo time().'waypo'; ?>"></script>
    <link rel="stylesheet" href="/static/plugs/bigview-main/css/comon0.css?v=<?php echo time().'comon'; ?>">
</head>
<body>
<input id="sn" type="hidden" value="<?php echo htmlentities($data['sn']); ?>">
<div class="loading">
    <div class="loadbox"><img src="/static/plugs/bigview-main/images/loading.gif"> 页面加载中...</div>
</div>

<div class="head_2">
    <h1><?php echo htmlentities($data['housename']); ?>环控数据</h1>
</div>
<div class="mainbox">
    <ul class="clearfix">
        <li>
            <div class="boxview" style="height:calc(33.3333% - .25rem)">
                <div class="titview01">一周室外温度</div>
                <div class="boxnav nav-view" id="echart_swwd"></div>
            </div>
            <div class="boxview" style="height:calc(33.3333% - .25rem);">
                <div class="titview01">一周温度曲线</div>
                <div class="boxnav nav-view" id="echart_wd"></div>
            </div>

            <div class="boxview" style="height:calc(33.3333% - .25rem)">
                <div class="titview01">一周湿度曲线</div>
                <div class="boxnav nav-view" id="echart_sd"></div>
            </div>
        </li>
        <li>
            <div class="box-middle-top" style="height:calc(48%)">
                <div class="titview02" id="datetime">时间</div>
                <div class="boxrow1" style="margin-bottom: .15rem">
                    <div class="boxitem1 boxitem-width-1">
                        <div class="itemtitle-2">蛋鸡品种</div>
                        <div class="itemcontent-2"><?php echo htmlentities($data['typename']); ?></div>
                    </div>
                    <div class="boxitem2 boxitem-width-1">
                        <div class="itemtitle-2">鸡群日龄(天)</div>
                        <div class="itemcontent-2"><?php echo htmlentities($data['days']); ?></div>
                    </div>
                    <div class="boxitem3 boxitem-width-1">
                        <div class="itemtitle-2">存栏数量(只)</div>
                        <div class="itemcontent-2"><?php echo htmlentities($data['cunlan']); ?></div>
                    </div>
                    <div class="boxitem4 boxitem-width-1">
                        <div class="itemtitle-2">报警数量</div>
                        <div class="itemcontent-2"><?php echo htmlentities($data['warnnum']); ?></div>
                    </div>
                </div>
                <div class="layui-row">
                    <input type="hidden" id="wd1_val" value="<?php echo htmlentities((isset($data['hjxx']['wd1']) && ($data['hjxx']['wd1'] !== '')?$data['hjxx']['wd1']:0)); ?>">
                    <input type="hidden" id="wd2_val" value="<?php echo htmlentities((isset($data['hjxx']['wd2']) && ($data['hjxx']['wd2'] !== '')?$data['hjxx']['wd2']:0)); ?>">
                    <input type="hidden" id="wd3_val" value="<?php echo htmlentities((isset($data['hjxx']['wd3']) && ($data['hjxx']['wd3'] !== '')?$data['hjxx']['wd3']:0)); ?>">
                    <input type="hidden" id="wd4_val" value="<?php echo htmlentities((isset($data['hjxx']['wd4']) && ($data['hjxx']['wd4'] !== '')?$data['hjxx']['wd4']:0)); ?>">
                    <input type="hidden" id="wd5_val" value="<?php echo htmlentities((isset($data['hjxx']['wd5']) && ($data['hjxx']['wd5'] !== '')?$data['hjxx']['wd5']:0)); ?>">
                    <input type="hidden" id="wd6_val" value="<?php echo htmlentities((isset($data['hjxx']['wd6']) && ($data['hjxx']['wd6'] !== '')?$data['hjxx']['wd6']:0)); ?>">
                    <input type="hidden" id="wd7_val" value="<?php echo htmlentities((isset($data['hjxx']['wd7']) && ($data['hjxx']['wd7'] !== '')?$data['hjxx']['wd7']:0)); ?>">
                    <input type="hidden" id="wd8_val" value="<?php echo htmlentities((isset($data['hjxx']['wd8']) && ($data['hjxx']['wd8'] !== '')?$data['hjxx']['wd8']:0)); ?>">
                    <input type="hidden" id="swwd_val" value="<?php echo htmlentities((isset($data['hjxx']['swwd']) && ($data['hjxx']['swwd'] !== '')?$data['hjxx']['swwd']:0)); ?>">
                    <input type="hidden" id="sd_val" value="<?php echo htmlentities((isset($data['hjxx']['sd']) && ($data['hjxx']['sd'] !== '')?$data['hjxx']['sd']:0)); ?>">
                    <input type="hidden" id="fy_val" value="<?php echo htmlentities((isset($data['hjxx']['fy']) && ($data['hjxx']['fy'] !== '')?$data['hjxx']['fy']:0)); ?>">
                    <input type="hidden" id="co2_val" value="<?php echo htmlentities((isset($data['hjxx']['co2']) && ($data['hjxx']['co2'] !== '')?$data['hjxx']['co2']:0)); ?>">
                    <input type="hidden" id="tfl_val" value="<?php echo htmlentities((isset($data['hjxx']['kztf']) && ($data['hjxx']['kztf'] !== '')?$data['hjxx']['kztf']:0)); ?>">
                    <div class="layui-col-md7">
                        <div style="display: flex;justify-content: space-around;margin-bottom: .1rem">
                            <div id="wd1" style="height:1rem" class="item-wd"></div>
<!--                            <div class="item-wd">-->
<!--                                <div class="itemtitle3">温度1</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd1']) && ($data['hjxx']['wd1'] !== '')?$data['hjxx']['wd1']:'')); ?></div>-->
<!--                            </div>-->
                            <div id="wd2" class="item-wd">
<!--                                <div class="itemtitle3">温度2</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd2']) && ($data['hjxx']['wd2'] !== '')?$data['hjxx']['wd2']:'')); ?>°C</div>-->
                            </div>
                            <div id="wd3" class="item-wd">
<!--                                <div class="itemtitle3">温度3</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd3']) && ($data['hjxx']['wd3'] !== '')?$data['hjxx']['wd3']:'')); ?>°C</div>-->
                            </div>
                            <div id="wd4" class="item-wd">
<!--                                <div class="itemtitle3">温度4</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd4']) && ($data['hjxx']['wd4'] !== '')?$data['hjxx']['wd4']:'')); ?>°C</div>-->
                            </div>
                        </div>
                        <div style="display: flex;justify-content: space-around">
                            <div id="wd5" class="item-wd">
<!--                                <div class="itemtitle3">温度5</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd5']) && ($data['hjxx']['wd5'] !== '')?$data['hjxx']['wd5']:'')); ?>°C</div>-->
                            </div>
                            <div id="wd6" class="item-wd">
<!--                                <div class="itemtitle3">温度6</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd6']) && ($data['hjxx']['wd6'] !== '')?$data['hjxx']['wd6']:'')); ?>°C</div>-->
                            </div>
                            <div id="wd7" class="item-wd">
<!--                                <div class="itemtitle3">温度7</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd7']) && ($data['hjxx']['wd7'] !== '')?$data['hjxx']['wd7']:'')); ?>°C</div>-->
                            </div>
                            <div id="wd8" class="item-wd">
<!--                                <div class="itemtitle3">温度8</div>-->
<!--                                <div class="itemcontent3"><?php echo htmlentities((isset($data['hjxx']['wd8']) && ($data['hjxx']['wd8'] !== '')?$data['hjxx']['wd8']:'')); ?>°C</div>-->
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md5">
                        <div style="display: flex;justify-content: space-around;padding:0 .5rem;margin-bottom: 0.1rem">
                            <div class="item-wd" id="swwd">
<!--                                <div class="itemtitle3"></div>-->
<!--                                <div class="itemcontent4"><?php echo htmlentities((isset($data['hjxx']['swwd']) && ($data['hjxx']['swwd'] !== '')?$data['hjxx']['swwd']:'')); ?>°C</div>-->
                            </div>
                            <div class="item-wd" id="sd">
<!--                                <div class="itemtitle3"></div>-->
<!--                                <div class="itemcontent4"><?php echo htmlentities((isset($data['hjxx']['sd1']) && ($data['hjxx']['sd1'] !== '')?$data['hjxx']['sd1']:'')); ?>%</div>-->
                            </div>
                        </div>
                        <div style="display: flex;justify-content: space-around">
                            <div class="item-wd" id="fy">
<!--                                <div class="itemtitle3"></div>-->
<!--                                <div class="itemcontent4"><?php echo htmlentities((isset($data['hjxx']['fy1']) && ($data['hjxx']['fy1'] !== '')?$data['hjxx']['fy1']:'')); ?>pa</div>-->
                            </div>
                            <div class="item-wd" id="co2">
<!--                                <div class="itemtitle3"></div>-->
<!--                                <div class="itemcontent4"><?php echo htmlentities((isset($data['hjxx']['co21']) && ($data['hjxx']['co21'] !== '')?$data['hjxx']['co21']:'')); ?>ppm</div>-->
                            </div>
                            <div class="item-wd" id="tfl">
<!--                                <div class="itemtitle3"></div>-->
<!--                                <div class="itemcontent4"><?php echo htmlentities((isset($data['hjxx']['kztfl']) && ($data['hjxx']['kztfl'] !== '')?$data['hjxx']['kztfl']:'')); ?>m3/h</div>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="box-middle-mid" style="height: calc(4%)"></div>
            <div class="box-middle-bottom" style="height:calc(48%);padding: 0.2rem 0.8rem;">
                <div style="display: flex;justify-content: space-between;margin-bottom: 0.1rem">
                    <?php $__FOR_START_1826590814__=1;$__FOR_END_1826590814__=9;for($i=$__FOR_START_1826590814__;$i < $__FOR_END_1826590814__;$i+=1){ if(isset($data['hjxx']['fj'.$i])): ?>
                    <div class="<?php echo $data['hjxx']['fj'.$i]==1 ? 'fj1' : 'fj0'; ?>">风机<?php echo htmlentities($i); ?></div>
                    <?php else: ?>
                    <div class="fjno">未配置</div>
                    <?php endif; } ?>
                </div>
                <div style="display: flex;justify-content: space-between;margin-bottom: 0.1rem">
                    <?php $__FOR_START_1596104490__=9;$__FOR_END_1596104490__=17;for($i=$__FOR_START_1596104490__;$i < $__FOR_END_1596104490__;$i+=1){ if(isset($data['hjxx']['fj'.$i])): ?>
                    <div class="<?php echo $data['hjxx']['fj'.$i]==1 ? 'fj1' : 'fj0'; ?>">风机<?php echo htmlentities($i); ?></div>
                    <?php else: ?>
                    <div class="fjno">未配置</div>
                    <?php endif; } ?>
                </div>
                <div style="display: flex;justify-content: space-between;margin-bottom: 0.1rem">
                    <?php $__FOR_START_1454568695__=17;$__FOR_END_1454568695__=25;for($i=$__FOR_START_1454568695__;$i < $__FOR_END_1454568695__;$i+=1){ if(isset($data['hjxx']['fj'.$i])): ?>
                    <div class="<?php echo $data['hjxx']['fj'.$i]==1 ? 'fj1' : 'fj0'; ?>">风机<?php echo htmlentities($i); ?></div>
                    <?php else: ?>
                    <div class="fjno">未配置</div>
                    <?php endif; } ?>

                </div>
                <div style="display: flex;justify-content: space-between;margin-bottom: 0.1rem">
                    <?php $__FOR_START_1215883376__=1;$__FOR_END_1215883376__=5;for($i=$__FOR_START_1215883376__;$i < $__FOR_END_1215883376__;$i+=1){ if(isset($data['hjxx']['xc'.$i])): ?>
                    <div class="<?php echo $data['hjxx']['xc'.$i]>0 ? 'fc1' : 'fc0'; ?>">
                        <div>风窗1</div>
                        <?php if($data['hjxx']['xc'.$i]>0): ?>
                        <div><?php echo htmlentities($data['hjxx']['xc'.$i]); ?>°</div>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="fcno">未配置</div>
                    <?php endif; } $__FOR_START_2004823716__=1;$__FOR_END_2004823716__=3;for($i=$__FOR_START_2004823716__;$i < $__FOR_END_2004823716__;$i+=1){ if(isset($data['hjxx']['bpfj'.$i])): ?>
                    <div class="<?php echo $data['hjxx']['bpfj'.$i]==1 ? 'bpfj1' : 'bpfj0'; ?>">变频风机<?php echo htmlentities($i); ?></div>
                    <?php else: ?>
                    <div class="bpfjno">未配置</div>
                    <?php endif; } if(isset($data['hjxx']['zm1'])): ?>
                    <div class="<?php echo $data['hjxx']['zm1']==1 ? 'zm1' : 'zm0'; ?>">照明</div>
                    <?php else: ?>
                    <div class="zmno">未配置</div>
                    <?php endif; ?>
                </div>
                <div style="display: flex;justify-content: space-between;margin-bottom: 0.1rem">
                    <?php if(isset($data['hjxx']['dfb1'])): ?>
                    <div class="<?php echo $data['hjxx']['dfb1']>0 ? 'dlbleft1' : 'dlbleft0'; ?>">
                        <div>山墙导流板</div>
                        <?php if($data['hjxx']['dfb1']>0): ?>
                        <div><?php echo htmlentities($data['hjxx']['dfb1']); ?>%</div>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="dlbleftno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['dfb2'])): ?>
                    <div class="<?php echo $data['hjxx']['dfb2']>0 ? 'dlbleft1' : 'dlbleft0'; ?>">
                        <div>左侧墙导流板</div>
                        <?php if($data['hjxx']['dfb1']>0): ?>
                        <div><?php echo htmlentities($data['hjxx']['dfb2']); ?>%</div>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="dlbleftno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['sl1'])): ?>
                    <div class="<?php echo $data['hjxx']['sl1']==1 ? 'sl1' : 'sl0'; ?>">山墙湿帘</div>
                    <?php else: ?>
                    <div class="slno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['sl2'])): ?>
                    <div class="<?php echo $data['hjxx']['sl2']==1 ? 'sl1' : 'sl0'; ?>">侧墙湿帘</div>
                    <?php else: ?>
                    <div class="slno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['pw1'])): ?>
                    <div class="<?php echo $data['hjxx']['pw1']==1 ? 'pw1' : 'pw0'; ?>">喷雾</div>
                    <?php else: ?>
                    <div class="pwno">未配置</div>
                    <?php endif; ?>
                </div>
                <div style="display: flex;justify-content: space-between;margin-bottom: 0.1rem">
                    <?php if(isset($data['hjxx']['bpjr1'])): ?>
                    <div class="<?php echo $data['hjxx']['bpjr1']==1 ? 'bpjr1' : 'bpjr0'; ?>">变频加热</div>
                    <?php else: ?>
                    <div class="bpjrno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['zmtgq1'])): ?>
                    <div class="<?php echo $data['hjxx']['zmtgq1']==1 ? 'bptg1' : 'bptg0'; ?>">变频调光</div>
                    <?php else: ?>
                    <div class="bptgno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['sksb1'])): ?>
                    <div class="<?php echo $data['hjxx']['sksb1']==1 ? 'sksb1' : 'sksb0'; ?>">时控设备</div>
                    <?php else: ?>
                    <div class="sksbno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['bj'])): ?>
                    <div class="<?php echo $data['hjxx']['bj']==1 ? 'bj1' : 'bj0'; ?>">报警设备</div>
                    <?php else: ?>
                    <div class="bjno">未配置</div>
                    <?php endif; if(isset($data['hjxx']['jr'])): ?>
                    <div class="<?php echo $data['hjxx']['jr']==1 ? 'jr1' : 'jr0'; ?>">加热</div>
                    <?php else: ?>
                    <div class="jrno">未配置</div>
                    <?php endif; ?>
                </div>
            </div>
        </li>
        <li>
            <div class="boxview" style="height:calc(33.3333% - .25rem)">
                <div class="titview01">一周二氧化碳曲线</div>
                <div class="boxnav nav-view" id="echart_co2"></div>
            </div>
            <div class="boxview" style="height:calc(33.3333% - .25rem);">
                <div class="titview01">一周负压曲线</div>
                <div class="boxnav nav-view" id="echart_fy"></div>
            </div>

            <div class="boxview" style="height:calc(33.3333% - .25rem)">
                <div class="titview01">报警记录</div>
                <div class="boxnav nav04">
                    <div class="listnav listnav2 scrollDiv">
                        <ul class="smjl">
                            <?php if(is_array($warningList) || $warningList instanceof \think\Collection || $warningList instanceof \think\Paginator): $i = 0; $__LIST__ = $warningList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li>
                                <span><?php echo htmlentities($vo['content']); ?></span>
                                <span class="text-green"><?php echo htmlentities($vo['createTime']); ?></span>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </li>
    </ul>
</div>


<script type="text/javascript">
    $('.counter').countUp();
    $(function () {

    });

</script>

<!--<script src="/static/plugs/bigview-main/js/countDown.js"></script>-->
<script type="text/javascript">

   $(window).load(function () {
       $(".loading").fadeOut()
   })

    /****/
    /****/
    $(document).ready(function () {
        var whei = $(window).width()
        $("html").css({fontSize: whei / 20})
        $(window).resize(function () {
            var whei = $(window).width()
            $("html").css({fontSize: whei / 20})
        });
    });

    $("input[name='countDown']").each(function () {
        var time_end = this.value;
        var con = $(this).next("span");
        var _ = this.dataset;
        countDown(con, {
            time_end: time_end//开始时间
        })
            //提供3个事件分别为:启动,重启,停止
            .on("countDownStarted countDownRestarted  countDownEnded ", function (arguments) {
                console.info(arguments);
            });
    });

</script>
</body>
</html>
</body>
</html>