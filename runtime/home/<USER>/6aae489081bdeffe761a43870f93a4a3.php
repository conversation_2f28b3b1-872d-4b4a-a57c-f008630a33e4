<?php /*a:2:{s:61:"/www/wwwroot/hksystem/app/home/<USER>/stats/envchart/index.html";i:1729492606;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<link rel="stylesheet" href="/static/plugs/bioarea/css/common.css">
<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form layuimini-form">
            <div class="layui-form-item">
                <label class="layui-form-label">养殖舍</label>
                <div class="layui-input-block">
                    <?php if(is_array($houseList) || $houseList instanceof \think\Collection || $houseList instanceof \think\Paginator): $i = 0; $__LIST__ = $houseList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <input type="checkbox" name="housename[<?php echo htmlentities($vo['id']); ?>]" lay-skin="primary" title="<?php echo htmlentities($vo['housename']); ?>">
                    <div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span><?php echo htmlentities($vo['housename']); ?></span><i class="layui-icon layui-icon-ok"></i></div>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </div>
            </div>

            <div class="layui-form-item" id="bioarea-wrap">
                <div class="layui-inline">
                    <label class="layui-form-label">日期</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="cdate" id="cdate" data-date="" data-date-type="date" value="<?php echo date('Y-m-d'); ?>">
                    </div>
                </div>
            </div>
            <input type="hidden" name="farmid" value="<?php echo htmlentities($farmid); ?>">
            <div class="layui-form-item text-center">
                <button type="submit" class="layui-btn layui-btn-normal layui-btn" lay-submit data-refresh="false">查看
                </button>
<!--                <button type="reset" class="layui-btn layui-btn-primary layui-btn">重置</button>-->
            </div>

        </form>
        <hr>

        <div class="layui-fluid">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <div id="echarts-wd1" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd2" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd3" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd4" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd5" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd6" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd7" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-wd8" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-pjwd" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-sd" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-co2" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-yl" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-swwd" style="width: 100%;min-height:300px"></div>
                </div>
                <div class="layui-col-md4">
                    <div id="echarts-swsd" style="width: 100%;min-height:300px"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        base: '/static/plugs/bioarea/js/'
    }).extend({
        bioarea: 'bioarea'
    });
</script>
<script>
    layui.use(['form', 'bioarea'], function () {

        var form = layui.form
            , bioarea = layui.bioarea

        bioarea.render({
            elem: '#bioarea-wrap',
            defaultData: {
                provinceCode: '',
            },
            form
        })

    });
</script>
</body>
</html>