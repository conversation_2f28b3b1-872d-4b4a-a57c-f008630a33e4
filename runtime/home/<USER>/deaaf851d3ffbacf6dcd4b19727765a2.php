<?php /*a:2:{s:49:"D:\Work\hksystem\app\home\view\index\welcome.html";i:1755429168;s:50:"D:\Work\hksystem\app\home\view\layout\default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<link rel="stylesheet" href="/static/home/<USER>/welcome.css?v=<?php echo time(); ?>" media="all">
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md7">
                        <div class="welcome-module">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <i class="fa fa-warning icon"></i>数据统计
                                </div>
                                <div class="layui-card-body">
                                    <div class="layui-row layui-col-space10">
                                        <?php 
                                        $stats = [
                                        ['title'=> '存栏数量', 'value'=> $data['cunlan'] ?? 0, 'unit'=> '当前存栏数量(只)', 'color'=> 'layui-bg-orange'],
                                        ['title'=> '在线设备数量', 'value'=> $data['onlineNum'] ?? 0, 'unit'=> '数量', 'color'=> 'layui-bg-blue'],
                                        ['title'=> '报警条数', 'value'=> $data['warnNum'] ?? 0, 'unit'=> '条数', 'color'=> 'layui-bg-red'],
                                        ['title'=> '月度用料', 'value'=> $data['feedNumSum'] ?? 0, 'unit'=> '当月耗料量(Kg)', 'color'=> 'layui-bg-cyan'],
                                        ['title'=> '月度用水', 'value'=> $data['waterNumSum'] ?? 0, 'unit'=> '当月用水量(吨)', 'color'=> 'layui-bg-green'],
                                        ['title'=> '月度用电', 'value'=> $data['powerNumSum'] ?? 0, 'unit'=> '当月用电量(度)', 'color'=> 'layui-bg-red'],
                                        ];
                                         foreach($stats as $item): ?>
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right <?php echo htmlentities($item['color']); ?>">实时</span>
                                                        <h5><?php echo htmlentities($item['title']); ?></h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins"><?php echo htmlentities($item['value']); ?></h1>
                                                        <small><?php echo htmlentities($item['unit']); ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="layui-col-md5">
                        <div class="welcome-module">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <i class="fa fa-credit-card icon icon-blue"></i>快捷入口
                                </div>
                                <div class="layui-card-body">
                                    <div class="layui-row layui-col-space10 layuimini-qiuck">
                                        <?php if(!(empty($quicks) || (($quicks instanceof \think\Collection || $quicks instanceof \think\Paginator ) && $quicks->isEmpty()))): foreach($quicks as $vo): ?>
                                        <div class="layui-col-xs4 layuimini-qiuck-module">
                                            <a layuimini-content-href="<?php echo url($vo['href']); ?>" data-title="<?php echo htmlentities($vo['title']); ?>">
                                                <i class="<?php echo $vo['icon']; ?>"></i><cite><?php echo htmlentities($vo['title']); ?></cite>
                                            </a>
                                        </div>
                                        <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="fa fa-line-chart icon"></i>环境数据
                            </div>
                            <div class="layui-card-body">
                                <div id="echarts-records" style="width: 100%;min-height:500px"></div>
                            </div>
                            <div id="wdval" class="layui-hide"></div>
                        </div>

                    </div>
                </div>

            </div>
            <div class="layui-col-md4">
                <?php 
                $cards = [
                    ['title'=> '系统公告', 'icon'=> 'fa fa-bullhorn', 'list'=> $articleList, 'moreUrl'=> '/home/<USER>/index?id=1', 'moreTitle'=> '公告消息', 'idKey'=> 'notice', 'extraKey'=> 'author'],
                    ['title'=> '未读消息', 'icon'=> 'fa fa-commenting-o', 'list'=> $msgList, 'moreUrl'=> '/home/<USER>/index', 'moreTitle'=> '系统消息', 'idKey'=> 'msg', 'extraKey'=> 'create_time'],
                ];
                 foreach($cards as $item): ?>
                <div class="welcome-module">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <div class="layui-row">
                                <div class="layui-col-sm6"><i class="<?php echo htmlentities($item['icon']); ?> icon icon-tip"></i><?php echo htmlentities($item['title']); ?></div>
                                <div class="layui-col-sm6" style="text-align: right">
                                    <a target="_self" layuimini-content-href="<?php echo htmlentities($item['moreUrl']); ?>" data-title="<?php echo htmlentities($item['moreTitle']); ?>" href="javascript:void(0)">
                                        <i class="fa fa-ellipsis-h icon icon-tip"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="layui-card-body layui-text">
                            <?php if(is_array($item['list']) || $item['list'] instanceof \think\Collection || $item['list'] instanceof \think\Paginator): $i = 0; $__LIST__ = $item['list'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <div class="layuimini-notice" id="<?php echo htmlentities($item['idKey']); ?>">
                                <div class="layuimini-notice-title"><?php echo htmlentities($vo['title']); ?></div>
                                <div class="layuimini-notice-extra"><?php echo htmlentities($vo['time']); ?></div>
                                <div class="layuimini-notice-content layui-hide"><?php echo htmlentities($vo['content']); ?></div>
                                <div style="display: none" id="author"><?php echo htmlentities($vo['author']); ?></div>
                            </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

            </div>
        </div>
    </div>
</div>







































</body>
</html>