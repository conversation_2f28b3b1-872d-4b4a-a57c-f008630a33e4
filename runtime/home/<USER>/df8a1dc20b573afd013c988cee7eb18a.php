<?php /*a:2:{s:54:"/www/wwwroot/hksystem/app/home/<USER>/index/welcome.html";i:1753022294;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<link rel="stylesheet" href="/static/home/<USER>/welcome.css?v=<?php echo time(); ?>" media="all">
<!--<script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=gv8MF9TECSOs2jYOGWOUm2pRbCtzis1y"></script>-->

<input type="hidden" id="auth" value="<?php echo session('admin.auth_ids'); ?>"/>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md7">
                        <div class="layui-card">
                            <div class="layui-card-header"><i class="fa fa-warning icon"></i>数据统计</div>
                            <div class="layui-card-body">
                                <div class="welcome-module">
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right layui-bg-orange">实时</span>
                                                        <h5>存栏数量</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins"><?php echo htmlentities((isset($data['cunlan']) && ($data['cunlan'] !== '')?$data['cunlan']:'0')); ?></h1>
                                                        <small>当前存栏数量(只)</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right layui-bg-blue">实时</span>
                                                        <h5>在线设备数量</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins"><?php echo htmlentities((isset($data['onlineNum']) && ($data['onlineNum'] !== '')?$data['onlineNum']:'0')); ?></h1>
                                                        <small>数量</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right layui-bg-red">实时</span>
                                                        <h5>报警条数</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins">
                                                            <?php echo htmlentities((isset($data['warnNum']) && ($data['warnNum'] !== '')?$data['warnNum']:'')); ?></h1>
                                                        <small>条数</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right layui-bg-cyan">实时</span>
                                                        <h5>月度用料</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins"><?php echo htmlentities((isset($data['feedNumSum']) && ($data['feedNumSum'] !== '')?$data['feedNumSum']:'0')); ?></h1>
                                                        <small>当月耗料量(Kg)</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right layui-bg-green">实时</span>
                                                        <h5>月度用水</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins">
                                                            <?php echo htmlentities((isset($data['waternum']) && ($data['waternum'] !== '')?$data['waternum']:'0')); ?></h1>
                                                        <small>当月用水量(吨)</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs4">
                                            <div class="panel layui-bg-number">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <span class="label pull-right layui-bg-red">实时</span>
                                                        <h5>月度用电</h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins">
                                                            <?php echo htmlentities((isset($data['powernum']) && ($data['powernum'] !== '')?$data['powernum']:'0')); ?></h1>
                                                        <small>当月用电量(度)</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md5">
                        <div class="layui-card">
                            <div class="layui-card-header"><i class="fa fa-credit-card icon icon-blue"></i>快捷入口</div>
                            <div class="layui-card-body">
                                <div class="welcome-module">
                                    <div class="layui-row layui-col-space10 layuimini-qiuck">
                                        <?php if(!(empty($quicks) || (($quicks instanceof \think\Collection || $quicks instanceof \think\Paginator ) && $quicks->isEmpty()))): foreach($quicks as $vo): ?>
                                        <div class="layui-col-xs4 layuimini-qiuck-module">
                                            <a layuimini-content-href="<?php echo url($vo['href']); ?>"
                                               data-title="<?php echo htmlentities((isset($vo['title']) && ($vo['title'] !== '')?$vo['title']:'')); ?>">
                                                <i class="<?php echo (isset($vo['icon']) && ($vo['icon'] !== '')?$vo['icon']:''); ?>"></i>
                                                <cite><?php echo htmlentities((isset($vo['title']) && ($vo['title'] !== '')?$vo['title']:'')); ?></cite>
                                            </a>
                                        </div>
                                        <?php endforeach; ?>
                                        <?php endif; ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header"><i class="fa fa-line-chart icon"></i>今天温度</div>
                            <div class="layui-card-body">
                                <div id="echarts-records" style="width: 100%;min-height:500px"></div>
                            </div>
                            <div id="wdval" class="layui-hide"></div>
                        </div>
                    </div>


                </div>


            </div>

            <div class="layui-col-md4">

                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-sm6"><i class="fa fa-bullhorn icon icon-tip"></i>系统公告</div>
                            <div class="layui-col-sm6 layui-nav-item" style="text-align: right">
                                <a target="_self" layuimini-content-href="/home/<USER>/index?id=1"
                                   data-title="公告信息" href="javascript:void(0)"><i
                                        class="fa fa-ellipsis-h icon icon-tip"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body layui-text">
                        <?php if(is_array($articleList) || $articleList instanceof \think\Collection || $articleList instanceof \think\Paginator): $i = 0; $__LIST__ = $articleList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <div class="layuimini-notice" id="notice">
                            <div class="layuimini-notice-title"><?php echo htmlentities($vo['title']); ?></div>
                            <div class="layuimini-notice-extra"><?php echo htmlentities($vo['create_time']); ?></div>
                            <div class="layuimini-notice-content layui-hide">
                                <?php echo htmlentities($vo['content']); ?>
                            </div>
                            <div style="display: none" id="author"><?php echo htmlentities($vo['author']); ?></div>
                        </div>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-sm6"><i class="fa fa-commenting-o icon icon-tip"></i>未读消息</div>
                            <div class="layui-col-sm6" style="text-align: right"><a target="_self"
                                                                                    layuimini-content-href="/home/<USER>/index.html"
                                                                                    data-title="系统消息"
                                                                                    href="javascript:void(0)"> <i
                                    class="fa fa-ellipsis-h icon icon-tip"></i></a></div>
                        </div>
                    </div>
                    <div class="layui-card-body layui-text">
                        <?php if(is_array($msgList) || $msgList instanceof \think\Collection || $msgList instanceof \think\Paginator): $i = 0; $__LIST__ = $msgList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <div class="layuimini-notice" id="msg">
                            <div class="layuimini-notice-title"><?php echo htmlentities($vo['title']); ?></div>
                            <div class="layuimini-notice-extra"><?php echo htmlentities($vo['create_time']); ?></div>
                            <div class="layuimini-notice-content layui-hide">
                                <?php echo htmlentities($vo['content']); ?>
                            </div>
                            <div id="id" class="layui-hide"><?php echo htmlentities($vo['id']); ?></div>
                        </div>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>
                </div>
                


            </div>
        </div>
    </div>
</div>

</body>
</html>