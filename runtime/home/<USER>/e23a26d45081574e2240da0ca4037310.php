<?php /*a:7:{s:60:"/www/wwwroot/hksystem/app/home/<USER>/system/config/index.html";i:1746233546;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;s:59:"/www/wwwroot/hksystem/app/home/<USER>/system/config/site.html";i:1729492606;s:59:"/www/wwwroot/hksystem/app/home/<USER>/system/config/logo.html";i:1729492606;s:60:"/www/wwwroot/hksystem/app/home/<USER>/system/config/price.html";i:1741769185;s:65:"/www/wwwroot/hksystem/app/home/<USER>/system/config/sellconfig.html";i:1729492606;s:62:"/www/wwwroot/hksystem/app/home/<USER>/system/config/product.html";i:1742804831;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main" id="app">

        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <ul class="layui-tab-title">
                <li>网站配置</li>
                <li class="layui-this">LOGO配置</li>
                <li>价格参数配置</li>
                <li>鸡蛋出入库单位</li>
                <li>生产相关配置</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item">
                    <form id="app-form" class="layui-form layuimini-form">

    <div class="layui-form-item">
        <label class="layui-form-label">站点名称</label>
        <div class="layui-input-block">
            <input type="text" name="site_name" class="layui-input" lay-verify="required" placeholder="请输入站点名称" value="<?php echo sysconfig('site','site_name'); ?>">
            <tip>填写站点名称。</tip>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">浏览器图标</label>
        <div class="layui-input-block layuimini-upload">
            <input name="site_ico" class="layui-input layui-col-xs6" lay-verify="required" placeholder="请上传浏览器图标,ico类型" value="<?php echo sysconfig('site','site_ico'); ?>">
            <div class="layuimini-upload-btn">
                <span><a class="layui-btn" data-upload="site_ico" data-upload-number="one" data-upload-exts="ico"><i class="fa fa-upload"></i> 上传</a></span>
                <span><a class="layui-btn layui-btn-normal" id="select_site_ico" data-upload-select="site_ico" data-upload-number="one"><i class="fa fa-list"></i> 选择</a></span>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">版本信息</label>
        <div class="layui-input-block">
            <input type="text" name="site_version" class="layui-input" lay-verify="required" placeholder="请输入版本信息" value="<?php echo sysconfig('site','site_version'); ?>">
            <tip>填写版本信息。</tip>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">备案信息</label>
        <div class="layui-input-block">
            <input type="text" name="site_beian" class="layui-input" lay-verify="required" placeholder="请输入备案信息" value="<?php echo sysconfig('site','site_beian'); ?>">
            <tip>填写备案信息。</tip>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">版权信息</label>
        <div class="layui-input-block">
            <input type="text" name="site_copyright" class="layui-input" lay-verify="required" placeholder="请输入版权信息" value="<?php echo sysconfig('site','site_copyright'); ?>">
            <tip>填写版权信息。</tip>
        </div>
    </div>

    <div class="hr-line"></div>
    <div class="layui-form-item text-center">
        <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit="system.config/save" data-refresh="false">确认</button>
        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
    </div>

</form>
                </div>
                <div class="layui-tab-item layui-show">
                    <form id="app-form" class="layui-form layuimini-form">

    <div class="layui-form-item">
        <label class="layui-form-label">LOGO标题</label>
        <div class="layui-input-block">
            <input type="text" name="logo_title" class="layui-input" lay-verify="required" placeholder="请输入LOGO标题" value="<?php echo logoConfig('logo_title'); ?>">
            <tip>填写名称,不多于4字。</tip>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">LOGO图标</label>
        <div class="layui-input-block layuimini-upload">
            <input name="logo_image" class="layui-input layui-col-xs6" lay-verify="required" placeholder="请上传LOGO图标" value="<?php echo logoConfig('logo_image'); ?>">
            <div class="layuimini-upload-btn">
                <span><a class="layui-btn" data-upload="logo_image" data-upload-number="one" data-upload-exts="ico|png|jpg|jpeg"><i class="fa fa-upload"></i> 上传</a></span>
                <span><a class="layui-btn layui-btn-normal" id="select_logo_image" data-upload-select="logo_image" data-upload-number="one"><i class="fa fa-list"></i> 选择</a></span>
            </div>
        </div>
    </div>

    <div class="hr-line"></div>
    <div class="layui-form-item text-center">
        <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit="system.config/save" data-refresh="false">确认</button>
        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
    </div>

</form>
                </div>
                <div class="layui-tab-item">
                    <form id="app-form" class="layui-form layuimini-form">

    <div class="layui-form-item">
        <label class="layui-form-label">电价（每度）</label>
        <div class="layui-input-block">
            <input type="text" name="power_price" class="layui-input"  placeholder="请输入电价（每度）" value="<?php echo configPrice('power_price'); ?>">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">水价（每吨）</label>
        <div class="layui-input-block">
            <input type="text" name="water_price" class="layui-input"  placeholder="请输入水价（每吨）" value="<?php echo configPrice('water_price'); ?>">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">料价（每千克）</label>
        <div class="layui-input-block">
            <input type="text" name="feed_price" class="layui-input"  placeholder="请输入料价（每吨）" value="<?php echo configPrice('feed_price'); ?>">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">蛋价（每千克）</label>
        <div class="layui-input-block">
            <input type="text" name="egg_price" class="layui-input"  placeholder="请输入蛋价（每Kg）" value="<?php echo configPrice('egg_price'); ?>">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">入库鸡蛋同步至生产</label>
        <div class="layui-input-block">
            <input type="checkbox" name="egg_sync" lay-skin="switch" value="1" lay-text="开启|关闭" <?php if(configPrice('egg_sync')==1): ?>checked<?php endif; ?>>
        </div>
    </div>
    <div class="hr-line"></div>
    <div class="layui-form-item text-center">
        <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit="system.config/savePrice" data-refresh="false">确认</button>
        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
    </div>

</form>
                </div>
                <div class="layui-tab-item">
                    <form id="app-form" class="layui-form layuimini-form">

    <div class="layui-form-item">
        <label class="layui-form-label">默认单位</label>
        <div class="layui-input-block">
            <select name="sellunit" >
                <option value=''></option>
                <?php foreach($getSellunitList as $k=>$v): ?>
                <option value='<?php echo htmlentities($v); ?>' <?php if(sellConfig('sellunit') == $v): ?>selected<?php endif; ?>><?php echo htmlentities($v); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">换算数量</label>
        <div class="layui-input-block">
            <input type="text" name="unitnum" class="layui-input"  placeholder="请输入换算数量" value="<?php echo sellConfig('unitnum'); ?>">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">换算单位</label>
        <div class="layui-input-block">
            <select name="munit">
                <option value="Kg" <?php if(sellConfig('munit') == 'Kg'): ?>selected<?php endif; ?>>Kg</option>
                <option value="枚" <?php if(sellConfig('munit') == '枚'): ?>selected<?php endif; ?>>枚</option>
            </select>
        </div>
    </div>

    <div class="hr-line"></div>
    <div class="layui-form-item text-center">
        <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit="system.config/saveSellConfig" data-refresh="false">确认</button>
        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
    </div>

</form>
                </div>
                <div class="layui-tab-item">
                    <form id="app-form" class="layui-form layuimini-form">

    <div class="layui-form-item">
        <label class="layui-form-label">水单位</label>
        <div class="layui-input-block">
            <select name="water_unit" >
                <option value=''></option>
                <?php foreach($weightList as $k=>$v): ?>
                <option value='<?php echo htmlentities($k); ?>' <?php if(configProduct('water_unit') == $k): ?>selected<?php endif; ?>><?php echo htmlentities($v); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">饲料单位</label>
        <div class="layui-input-block">
            <select name="feed_unit" >
                <option value=''></option>
                <?php foreach($weightList as $k=>$v): ?>
                <option value='<?php echo htmlentities($k); ?>' <?php if(configProduct('feed_unit') == $k): ?>selected<?php endif; ?>><?php echo htmlentities($v); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">鸡蛋重量单位</label>
        <div class="layui-input-block">
            <select name="egg_unit" >
                <option value=''></option>
                <?php foreach($weightList as $k=>$v): ?>
                <option value='<?php echo htmlentities($k); ?>' <?php if(configProduct('egg_unit') == $k): ?>selected<?php endif; ?>><?php echo htmlentities($v); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">入库鸡蛋同步至生产</label>
        <div class="layui-input-block">
            <input type="checkbox" name="egg_sync" lay-skin="switch" value="1" lay-text="开启|关闭" <?php if(configProduct('egg_sync')==1): ?>checked<?php endif; ?>>
        </div>
    </div>
    <div class="hr-line"></div>
    <div class="layui-form-item text-center">
        <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit="system.config/saveProduct" data-refresh="false">确认</button>
        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
    </div>

</form>
                </div>
            </div>
        </div>

    </div>
</div>
</body>
</html>