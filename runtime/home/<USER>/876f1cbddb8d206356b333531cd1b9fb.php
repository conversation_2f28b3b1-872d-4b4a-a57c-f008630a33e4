<?php /*a:2:{s:60:"/www/wwwroot/hksystem/app/home/<USER>/system/myinfo/index.html";i:1729492606;s:55:"/www/wwwroot/hksystem/app/home/<USER>/layout/default.html";i:1729492604;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo sysconfig('site','site_name'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/home/<USER>/public.css?v=<?php echo htmlentities($version); ?>" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo htmlentities((isset($adminModuleName) && ($adminModuleName !== '')?$adminModuleName:'admin')); ?>",
            CONTROLLER_JS_PATH: "<?php echo htmlentities((isset($thisControllerJsPath) && ($thisControllerJsPath !== '')?$thisControllerJsPath:'')); ?>",
            ACTION: "<?php echo htmlentities((isset($thisAction) && ($thisAction !== '')?$thisAction:'')); ?>",
            AUTOLOAD_JS: "<?php echo htmlentities((isset($autoloadJs) && ($autoloadJs !== '')?$autoloadJs:'false')); ?>",
            IS_SUPER_ADMIN: "<?php echo htmlentities((isset($isSuperAdmin) && ($isSuperAdmin !== '')?$isSuperAdmin:'false')); ?>",
            VERSION: "<?php echo htmlentities((isset($version) && ($version !== '')?$version:'1.0.0')); ?>",
        };
    </script>
    <script src="/static/plugs/layui-v2.5.6/layui.all.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo htmlentities($version); ?>" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main" id="app">

        <form id="app-form" class="layui-form layuimini-form">

            <div class="layui-form-item">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="lxname" class="layui-input"  lay-verify="required" placeholder="请输入姓名" value="<?php echo htmlentities((isset($row['lxname']) && ($row['lxname'] !== '')?$row['lxname']:'')); ?>">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">养殖场名称</label>
                <div class="layui-input-block">
                    <input type="text" name="farmname" class="layui-input"  lay-verify="required" placeholder="请输入养殖场名称" value="<?php echo htmlentities((isset($row['farmname']) && ($row['farmname'] !== '')?$row['farmname']:'')); ?>">
                </div>
            </div>
            <div class="layui-form-item" id="bioarea-wrap">
                <label class="layui-form-label required">所属地域</label>
                <label class="layui-form-label small-width">省</label>
                <div class="layui-input-inline">
                    <select style="width: 100px;" class="level-1-select" lay-filter="#bioarea-wrap1level"
                            name="province">
                        <option value="">请选择省</option>
                    </select>
                </div>
                <label class="layui-form-label small-width">市</label>
                <div class="layui-input-inline">
                    <select class="level-2-select" lay-filter="#bioarea-wrap2level" name="city">
                        <option value="">请选择市</option>
                    </select>
                </div>
                <label class="layui-form-label small-width">区县</label>
                <div class="layui-input-inline">
                    <select class="level-3-select" lay-filter="#bioarea-wrap3level" name="county">
                        <option value="">请选择区</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">地址</label>
                <div class="layui-input-block">
                    <input type="text" name="address" class="layui-input"  placeholder="请输入地址" value="<?php echo htmlentities((isset($row['address']) && ($row['address'] !== '')?$row['address']:'')); ?>">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">电话</label>
                <div class="layui-input-block">
                    <input type="text" name="lxtel" class="layui-input"  lay-verify="required" placeholder="请输入电话" value="<?php echo htmlentities((isset($row['lxtel']) && ($row['lxtel'] !== '')?$row['lxtel']:'')); ?>">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">经度</label>
                <div class="layui-input-block">
                    <input type="text" name="longitude" class="layui-input"  placeholder="请输入经度" value="<?php echo htmlentities((isset($row['longitude']) && ($row['longitude'] !== '')?$row['longitude']:'')); ?>">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">纬度</label>
                <div class="layui-input-block">
                    <input type="text" name="latitude" class="layui-input"  placeholder="请输入纬度" value="<?php echo htmlentities((isset($row['latitude']) && ($row['latitude'] !== '')?$row['latitude']:'')); ?>">
                </div>
            </div>
            <div class="hr-line"></div>
            <div class="layui-form-item text-center">
                <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit="system.myinfo/save" data-refresh="false">保存</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>

        </form>

    </div>
</div>
<script>
    layui.config({
        base: '/static/plugs/bioarea/js/'
    }).extend({
        bioarea: 'bioarea'
    });
</script>
<script>
    layui.use(['form', 'bioarea'], function() {

        var form = layui.form
            , bioarea = layui.bioarea
        var province='<?php echo htmlentities($row['province']); ?>';
        var city='<?php echo htmlentities($row['city']); ?>';
        var county='<?php echo htmlentities($row['county']); ?>';
        bioarea.render({
            elem: '#bioarea-wrap',
            defaultData: {
                provinceCode: province,
                cityCode: city,
                countyCode: county
            },
            form
        })
        //监听提交
        form.on('submit(demo1)', function(data) {
            layer.alert(JSON.stringify(data.field), {
                title: '最终的提交信息'
            })
            return false;
        });


        //表单取值
        layui.$('#LAY-component-form-getval').on('click', function() {
            var data = form.val('example');
            console.log(data);
            alert(JSON.stringify(data));
        });

    });
</script>
</body>
</html>