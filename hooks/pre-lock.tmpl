#!/bin/sh

# PRE-LOCK HOOK
#
# The pre-lock hook is invoked before an exclusive lock is
# created.  Subversion runs this hook by invoking a program 
# (script, executable, binary, etc.) named 'pre-lock' (for which
# this file is a template), with the following ordered arguments:
#
#   [1] REPOS-PATH   (the path to this repository)
#   [2] PATH         (the path in the repository about to be locked)
#   [3] USER         (the user creating the lock)
#   [4] COMMENT      (the comment of the lock)
#   [5] STEAL-LOCK   (1 if the user is trying to steal the lock, else 0)
#
# If the hook program outputs anything on stdout, the output string will
# be used as the lock token for this lock operation.  If you choose to use
# this feature, you must guarantee the tokens generated are unique across
# the repository each time.
#
# If the hook program exits with success, the lock is created; but
# if it exits with failure (non-zero), the lock action is aborted
# and STDERR is returned to the client.
#
# The default working directory for the invocation is undefined, so
# the program should set one explicitly if it cares.
#
# On a Unix system, the normal procedure is to have 'pre-lock'
# invoke other programs to do the real work, though it may do the
# work itself too.
#
# Note that 'pre-lock' must be executable by the user(s) who will
# invoke it (typically the user httpd runs as), and that user must
# have filesystem-level permission to access the repository.
#
# On a Windows system, you should name the hook program
# 'pre-lock.bat' or 'pre-lock.exe',
# but the basic idea is the same.
#
# The hook program runs in an empty environment, unless the server is
# explicitly configured otherwise.  For example, a common problem is for
# the PATH environment variable to not be set to its usual value, so
# that subprograms fail to launch unless invoked via absolute path.
# If you're having unexpected problems with a hook program, the
# culprit may be unusual (or missing) environment variables.
#
# CAUTION:
# For security reasons, you MUST always properly quote arguments when
# you use them, as those arguments could contain whitespace or other
# problematic characters. Additionally, you should delimit the list
# of options with "--" before passing the arguments, so malicious
# clients cannot bootleg unexpected options to the commands your
# script aims to execute.
# For similar reasons, you should also add a trailing @ to URLs which
# are passed to SVN commands accepting URLs with peg revisions.
#
# Here is an example hook script, for a Unix /bin/sh interpreter.
# For more examples and pre-written hooks, see those in
# the Subversion repository at
# http://svn.apache.org/repos/asf/subversion/trunk/tools/hook-scripts/ and
# http://svn.apache.org/repos/asf/subversion/trunk/contrib/hook-scripts/


REPOS="$1"
PATH="$2"
USER="$3"
COMMENT="$4"
STEAL="$5"

# If a lock exists and is owned by a different person, don't allow it
# to be stolen (e.g., with 'svn lock --force ...').

# (Maybe this script could send email to the lock owner?)
SVNLOOK=/usr/local/bin/svnlook
GREP=/bin/grep
SED=/bin/sed

LOCK_OWNER=`$SVNLOOK lock "$REPOS" "$PATH" | \
            $GREP '^Owner: ' | $SED 's/Owner: //'`

# If we get no result from svnlook, there's no lock, allow the lock to
# happen:
if [ "$LOCK_OWNER" = "" ]; then
  exit 0
fi

# If the person locking matches the lock's owner, allow the lock to
# happen:
if [ "$LOCK_OWNER" = "$USER" ]; then
  exit 0
fi

# Otherwise, we've got an owner mismatch, so return failure:
echo "Error: $PATH already locked by ${LOCK_OWNER}." 1>&2
exit 1
