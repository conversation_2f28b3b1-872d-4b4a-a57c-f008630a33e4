#!/bin/sh

# START-<PERSON>MMIT HOOK
#
# The start-commit hook is invoked immediately after a Subversion txn is
# created and populated with initial revprops in the process of doing a
# commit. Subversion runs this hook by invoking a program (script, 
# executable, binary, etc.) named 'start-commit' (for which this file
# is a template) with the following ordered arguments:
#
#   [1] REPOS-PATH   (the path to this repository)
#   [2] USER         (the authenticated user attempting to commit)
#   [3] CAPABILITIES (a colon-separated list of capabilities reported
#                     by the client; see note below)
#   [4] TXN-NAME     (the name of the commit txn just created)
#
# Note: The CAPABILITIES parameter is new in Subversion 1.5, and 1.5
# clients will typically report at least the "mergeinfo" capability.
# If there are other capabilities, then the list is colon-separated,
# e.g.: "mergeinfo:some-other-capability" (the order is undefined).
#
# The list is self-reported by the client.  Therefore, you should not
# make security assumptions based on the capabilities list, nor should
# you assume that clients reliably report every capability they have.
#
# Note: The TXN-NAME parameter is new in Subversion 1.8.  Prior to version
# 1.8, the start-commit hook was invoked before the commit txn was even
# created, so the ability to inspect the commit txn and its metadata from
# within the start-commit hook was not possible.
# 
# If the hook program exits with success, the commit continues; but
# if it exits with failure (non-zero), the commit is stopped before
# a Subversion txn is created, and STDERR is returned to the client.
#
# The default working directory for the invocation is undefined, so
# the program should set one explicitly if it cares.
#
# On a Unix system, the normal procedure is to have 'start-commit'
# invoke other programs to do the real work, though it may do the
# work itself too.
#
# Note that 'start-commit' must be executable by the user(s) who will
# invoke it (typically the user httpd runs as), and that user must
# have filesystem-level permission to access the repository.
#
# On a Windows system, you should name the hook program
# 'start-commit.bat' or 'start-commit.exe',
# but the basic idea is the same.
#
# The hook program runs in an empty environment, unless the server is
# explicitly configured otherwise.  For example, a common problem is for
# the PATH environment variable to not be set to its usual value, so
# that subprograms fail to launch unless invoked via absolute path.
# If you're having unexpected problems with a hook program, the
# culprit may be unusual (or missing) environment variables.
#
# CAUTION:
# For security reasons, you MUST always properly quote arguments when
# you use them, as those arguments could contain whitespace or other
# problematic characters. Additionally, you should delimit the list
# of options with "--" before passing the arguments, so malicious
# clients cannot bootleg unexpected options to the commands your
# script aims to execute.
# For similar reasons, you should also add a trailing @ to URLs which
# are passed to SVN commands accepting URLs with peg revisions.
#
# Here is an example hook script, for a Unix /bin/sh interpreter.
# For more examples and pre-written hooks, see those in
# the Subversion repository at
# http://svn.apache.org/repos/asf/subversion/trunk/tools/hook-scripts/ and
# http://svn.apache.org/repos/asf/subversion/trunk/contrib/hook-scripts/


REPOS="$1"
USER="$2"

commit-allower.pl --repository "$REPOS" --user "$USER" || exit 1
special-auth-check.py --user "$USER" --auth-level 3 || exit 1

# All checks passed, so allow the commit.
exit 0
