#!/bin/sh

# PRE-<PERSON>LOCK HOOK
#
# The pre-unlock hook is invoked before an exclusive lock is
# destroyed.  Subversion runs this hook by invoking a program 
# (script, executable, binary, etc.) named 'pre-unlock' (for which
# this file is a template), with the following ordered arguments:
#
#   [1] REPOS-PATH   (the path to this repository)
#   [2] PATH         (the path in the repository about to be unlocked)
#   [3] USER         (the user destroying the lock)
#   [4] TOKEN        (the lock token to be destroyed)
#   [5] BREAK-UNLOCK (1 if the user is breaking the lock, else 0)
#
# If the hook program exits with success, the lock is destroyed; but
# if it exits with failure (non-zero), the unlock action is aborted
# and STDERR is returned to the client.
#
# The default working directory for the invocation is undefined, so
# the program should set one explicitly if it cares.
#
# On a Unix system, the normal procedure is to have 'pre-unlock'
# invoke other programs to do the real work, though it may do the
# work itself too.
#
# Note that 'pre-unlock' must be executable by the user(s) who will
# invoke it (typically the user httpd runs as), and that user must
# have filesystem-level permission to access the repository.
#
# On a Windows system, you should name the hook program
# 'pre-unlock.bat' or 'pre-unlock.exe',
# but the basic idea is the same.
#
# The hook program runs in an empty environment, unless the server is
# explicitly configured otherwise.  For example, a common problem is for
# the PATH environment variable to not be set to its usual value, so
# that subprograms fail to launch unless invoked via absolute path.
# If you're having unexpected problems with a hook program, the
# culprit may be unusual (or missing) environment variables.
#
# CAUTION:
# For security reasons, you MUST always properly quote arguments when
# you use them, as those arguments could contain whitespace or other
# problematic characters. Additionally, you should delimit the list
# of options with "--" before passing the arguments, so malicious
# clients cannot bootleg unexpected options to the commands your
# script aims to execute.
# For similar reasons, you should also add a trailing @ to URLs which
# are passed to SVN commands accepting URLs with peg revisions.
#
# Here is an example hook script, for a Unix /bin/sh interpreter.
# For more examples and pre-written hooks, see those in
# the Subversion repository at
# http://svn.apache.org/repos/asf/subversion/trunk/tools/hook-scripts/ and
# http://svn.apache.org/repos/asf/subversion/trunk/contrib/hook-scripts/


REPOS="$1"
PATH="$2"
USER="$3"
TOKEN="$4"
BREAK="$5"

# If a lock is owned by a different person, don't allow it be broken.
# (Maybe this script could send email to the lock owner?)

SVNLOOK=/usr/local/bin/svnlook
GREP=/bin/grep
SED=/bin/sed

LOCK_OWNER=`$SVNLOOK lock "$REPOS" "$PATH" | \
            $GREP '^Owner: ' | $SED 's/Owner: //'`

# If we get no result from svnlook, there's no lock, return success:
if [ "$LOCK_OWNER" = "" ]; then
  exit 0
fi

# If the person unlocking matches the lock's owner, return success:
if [ "$LOCK_OWNER" = "$USER" ]; then
  exit 0
fi

# Otherwise, we've got an owner mismatch, so return failure:
echo "Error: $PATH locked by ${LOCK_OWNER}." 1>&2
exit 1
